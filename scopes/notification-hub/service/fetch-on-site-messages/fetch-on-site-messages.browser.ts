/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2021-12-7
 *
 * @packageDocumentation
 */

import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-on-site-messages';
import type { SvcQuery, SvcRespData, RequestRespData, ApiQ } from './fetch-on-site-messages.type';
import {
  BackendOnSiteMessageState,
  BackendOnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
/**
 * 获取站内信表格数据
 *
 * @see [Doc](http://172.16.0.17:13000/project/90/interface/api/2536)
 *
 * @param variant
 * @returns
 */
export async function fetchOnSiteMessages(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = {
    startTime: query.timeRange?.[0],
    endTime: query.timeRange?.[1],
    isRead: query.state ? BackendOnSiteMessageState[query.state] : undefined,
    insideMsgPType: BackendOnSiteMessageType[query.type],
    pageNum: query.page,
    pageSize: query.pageSize,
    insideMsgCType: query.subType,
  };
  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, ApiQ>(
    endpoint,
    params
  );

  if (error || data === null || data.data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }

  return { error, data: { data: data.data, total: data.total }, ...rest };
}
