import React, { useMemo } from 'react';

import { Form } from '@manyun/base-ui.h5.ui.form';
import { Input } from '@manyun/base-ui.h5.ui.input';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import type { PopupRef } from '@manyun/base-ui.h5.ui.popup';
import { Selector } from '@manyun/base-ui.h5.ui.selector';
import { TextArea } from '@manyun/base-ui.h5.ui.textarea';
import { getSaleQuoteLocales, transformToBoolean } from '@manyun/crm.model.sale-quote';
import type { SaleQuoteJSON, ServiceFeePayWay, TrueOrFalse } from '@manyun/crm.model.sale-quote';
import { useIntl } from '@manyun/dc-brain.h5.store.store';

export type SuperChargeInfos = Partial<
  Pick<
    SaleQuoteJSON,
    | 'estimatedCostInfoCost'
    | 'estimatedCostInfoIncome'
    | 'estimatedCostInfoIncomeRate'
    | 'estimatedCostInfoPayMethod'
    | 'estimatedCostInfoComment'
  > & { hasEstimatedCost: TrueOrFalse }
>;

type EstimatedCostPopupProps = {
  value?: SuperChargeInfos;
  onChange?: (value: SuperChargeInfos) => void;
};

export const EstimatedCostPopup = React.forwardRef<PopupRef, EstimatedCostPopupProps>(
  ({ value, onChange }, ref) => {
    const intl = useIntl();
    const [form] = Form.useForm<SuperChargeInfos>();
    const hasEstimatedCost = Form.useWatch('hasEstimatedCost', form);
    const locales = useMemo(() => getSaleQuoteLocales(), []);

    return (
      <>
        <Popup
          ref={ref}
          title={locales.idcInfos.predictReformCost}
          bodyStyle={{ height: '80vh' }}
          contentStyle={{ padding: '10px  0 0 0 ' }}
          footer
          arrow
          destroyOnClose
          onFooterClick={(type, actions) => {
            if (type === 'ok') {
              form.validateFields().then(values => {
                onChange?.(values);
                actions?.close();
              });
            }
          }}
          onMaskClick={() => {
            form.resetFields();
          }}
          onVisible={() => {
            form.setFieldsValue({ ...value });
          }}
        >
          <Form style={{ padding: 0, '--prefix-width': 'auto' }} layout="horizontal" form={form}>
            <Form.Item
              name="hasEstimatedCost"
              layout="vertical"
              getValueFromEvent={value => {
                return Array.isArray(value) ? value[0] : value;
              }}
              getValueProps={value => {
                return { value: value ? [value] : undefined };
              }}
            >
              <Selector
                columns={2}
                options={[
                  { label: '无', value: 'FALSE' },
                  { label: '有', value: 'TRUE' },
                ]}
                onChange={value => {
                  if (value[0] === 'FALSE') {
                    form.setFieldsValue({
                      estimatedCostInfoCost: undefined,
                      estimatedCostInfoIncome: undefined,
                      estimatedCostInfoIncomeRate: undefined,
                      estimatedCostInfoPayMethod: undefined,
                      estimatedCostInfoComment: undefined,
                    });
                  }
                }}
              />
            </Form.Item>
            {hasEstimatedCost === 'TRUE' && (
              <>
                <Form.Item
                  name="estimatedCostInfoCost"
                  label={locales.idcInfos.predictReformCostInfo.cost}
                  rules={[{ required: true, message: '请输入成本' }]}
                  extra="元"
                >
                  <Input
                    style={{ '--text-align': 'right' }}
                    type="number"
                    max={999999999.99}
                    min={0.01}
                    precision={2}
                  />
                </Form.Item>
                <Form.Item
                  name="estimatedCostInfoIncome"
                  label={locales.idcInfos.predictReformCostInfo.income}
                  rules={[{ required: true, message: '请输入收益' }]}
                  extra="元"
                >
                  <Input
                    style={{ '--text-align': 'right' }}
                    type="number"
                    min={0.01}
                    max={999999999.99}
                    precision={2}
                  />
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(prevValues, currentValues) =>
                    prevValues.estimatedCostInfoCost !== currentValues.estimatedCostInfoCost ||
                    prevValues.estimatedCostInfoIncome !== currentValues.estimatedCostInfoIncome
                  }
                >
                  {({ getFieldValue, setFieldValue }) => {
                    const cost = Number(getFieldValue('estimatedCostInfoCost'));
                    const income = Number(getFieldValue('estimatedCostInfoIncome'));
                    if (typeof cost === 'number' && typeof income === 'number' && cost && income) {
                      const rate = Math.round(((income - cost) / cost) * 100);
                      setFieldValue('estimatedCostInfoIncomeRate', rate);
                    } else {
                      setFieldValue('estimatedCostInfoIncomeRate', '');
                    }

                    return (
                      <Form.Item
                        name="estimatedCostInfoIncomeRate"
                        label={locales.idcInfos.predictReformCostInfo.incomeRate}
                        rules={[{ required: true, message: '收益率必填' }]}
                        extra="%"
                      >
                        <Input style={{ '--text-align': 'right' }} type="number" disabled />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
                <Form.Item
                  name="estimatedCostInfoPayMethod"
                  label={locales.idcInfos.predictReformCostInfo.payMethod._self}
                  layout="vertical"
                  getValueFromEvent={value => {
                    return Array.isArray(value) ? value[0] : value;
                  }}
                  getValueProps={value => {
                    return { value: value ? [value] : undefined };
                  }}
                  space={false}
                  divider
                  rules={[{ required: true, message: '请选择付款方式' }]}
                >
                  <Selector
                    columns={2}
                    options={Object.keys(locales.idcInfos.predictReformCostInfo.payMethod.enum).map(
                      key => ({
                        label:
                          locales.idcInfos.predictReformCostInfo.payMethod.enum[
                            key as ServiceFeePayWay
                          ],
                        value: key,
                      })
                    )}
                  />
                </Form.Item>
                <Form.Item
                  name="estimatedCostInfoComment"
                  label={locales.idcInfos.predictReformCostInfo.comment}
                  layout="vertical"
                  rules={[
                    {
                      whitespace: true,
                      validator: (_, val) => {
                        if ((val || '').length < 501) {
                          return Promise.resolve();
                        }
                        return Promise.reject(locales.commonValidLimit.noteMaxLen);
                      },
                    },
                  ]}
                >
                  <TextArea autoSize={{ minRows: 3 }} maxLength={501} />
                </Form.Item>
              </>
            )}
          </Form>
        </Popup>
        {value && value.hasEstimatedCost ? (
          `${transformToBoolean(value.hasEstimatedCost) ? '有' : '无'}`
        ) : (
          <span style={{ color: 'var(--adm-color-light)' }}>
            {intl.getMessage('common.placeholder', '请选择')}
          </span>
        )}
      </>
    );
  }
);

EstimatedCostPopup.displayName = 'EstimatedCostPopup';
