import { RightOutlined } from '@ant-design/icons';
import React, { useState } from 'react';

import { Input } from '@manyun/base-ui.h5.ui.input';
import { UserPicker } from '@manyun/iam.h5.ui.user-picker';

interface TransformUserProps {
  applyUser: number;
  approveUsers: number[];
  blockGuid: string | null;
  handleSelect: (value: Record<string, any>) => void;
}

export const TransformUser = ({
  applyUser,
  approveUsers,
  blockGuid,
  handleSelect,
}: TransformUserProps) => {
  const [visible, setVisible] = useState(false);
  const [redirectUser, setRedirectUser] = useState('');

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <div style={{ color: ' #4E5969', fontSize: '14px' }}>{redirectUser}</div>
      <div
        style={{ height: '30px', position: 'relative', top: ' -8px' }}
        onClick={() => {
          setVisible(!visible);
        }}
      >
        <RightOutlined style={{ color: '#c6cad1' }} />
      </div>
      <UserPicker
        showLabel={false}
        visible={visible}
        filterCurrentUser
        applyUserId={applyUser}
        approvalUserIds={approveUsers}
        resourceCode={blockGuid ?? undefined}
        onChange={value => {
          setRedirectUser(value?.label);
          handleSelect(value);
        }}
        onMaskClick={() => {
          setVisible(false);
        }}
        onClickArrow={() => {
          setVisible(false);
        }}
      />
    </div>
  );
};
