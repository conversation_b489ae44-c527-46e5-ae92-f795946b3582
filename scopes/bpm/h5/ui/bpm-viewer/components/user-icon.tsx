import React from 'react';

export default ({ fill }: Record<string, any>) => (
  <svg width="14" height="14" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M1.79999 3.40403C1.79999 3.7192 1.86207 4.03129 1.98268 4.32247C2.10329 4.61365 2.28007 4.87822 2.50293 5.10108C2.72579 5.32395 2.99037 5.50073 3.28155 5.62134C3.57273 5.74195 3.88481 5.80403 4.19999 5.80403C4.51516 5.80403 4.82725 5.74195 5.11843 5.62134C5.40961 5.50073 5.67418 5.32395 5.89704 5.10108C6.1199 4.87822 6.29669 4.61365 6.4173 4.32247C6.53791 4.03129 6.59999 3.7192 6.59999 3.40403C6.59999 2.76751 6.34713 2.15706 5.89704 1.70697C5.44696 1.25688 4.83651 1.00403 4.19999 1.00403C3.56347 1.00403 2.95302 1.25688 2.50293 1.70697C2.05284 2.15706 1.79999 2.76751 1.79999 3.40403Z"
      fill={fill}
    />
    <path
      d="M4.33319 6.60399C2.12399 6.60399 0.333191 8.39559 0.333191 10.604C0.333191 10.7392 0.340391 10.8728 0.353591 11.004H8.31279C8.32559 10.8728 8.33319 10.7392 8.33319 10.604C8.33319 8.39439 6.54199 6.60399 4.33319 6.60399ZM6.90839 5.10879C6.86628 5.16571 6.83999 5.23276 6.8322 5.30313C6.82441 5.3735 6.8354 5.44467 6.86406 5.50942C6.89271 5.57416 6.938 5.63016 6.99532 5.67171C7.05264 5.71327 7.11995 5.7389 7.19039 5.74599C7.19439 5.74639 7.50519 5.80439 7.66559 5.80439C8.30211 5.80439 8.91256 5.55154 9.36265 5.10145C9.81274 4.65136 10.0656 4.04091 10.0656 3.40439C10.0656 2.76787 9.81274 2.15743 9.36265 1.70734C8.91256 1.25725 8.30211 1.00439 7.66559 1.00439C7.50519 1.00439 7.19399 1.06199 7.19039 1.06239C7.11989 1.06937 7.0525 1.09493 6.9951 1.13647C6.9377 1.178 6.89235 1.23402 6.86368 1.29881C6.83501 1.36359 6.82404 1.43483 6.83189 1.50524C6.83975 1.57565 6.86615 1.64272 6.90839 1.69959C6.91519 1.70839 6.91679 1.71439 6.91679 1.71959C7.23335 2.22432 7.40085 2.80822 7.39999 3.40399C7.40067 3.99976 7.23348 4.58367 6.91759 5.08879C6.91646 5.09619 6.91327 5.10312 6.90839 5.10879ZM7.66639 6.60399C7.61759 6.60399 7.56999 6.608 7.52199 6.60959C7.49131 6.61372 7.46203 6.62497 7.43649 6.64246C7.41095 6.65995 7.38987 6.68318 7.37493 6.71029C7.36 6.7374 7.35163 6.76763 7.35049 6.79856C7.34935 6.82949 7.35548 6.86026 7.36839 6.88839C7.36959 6.89079 7.36719 6.89399 7.36759 6.89679C8.44239 7.77599 9.13319 9.10639 9.13319 10.6052C9.13319 10.7404 9.11919 10.8724 9.10799 11.0052H11.6468C11.6596 10.8732 11.6668 10.7404 11.6668 10.6052C11.6664 8.39439 9.87519 6.60399 7.66639 6.60399Z"
      fill={fill}
    />
  </svg>
);
