import React, { useState } from 'react';

import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { UserAvatar, UserName } from '@manyun/iam.h5.ui.user';

import UserIcon from './user-icon';

interface IPartUserProps {
  commentAssigner: string;
}

export function PartUser({ commentAssigner }: IPartUserProps) {
  const commentAssignerArr = commentAssigner.split(',') || [];
  const [v, setV] = useState(false);

  const handleClick = () => {
    setV(!v);
  };

  const handleClose = () => {
    setV(false);
  };

  if (!commentAssignerArr.length) {
    return;
  }

  return (
    <div style={{ marginTop: 4 }}>
      <div
        onClick={handleClick}
        style={{
          textAlign: 'right',
          color: 'var(--adm-color-primary)',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <UserIcon fill="var(--adm-color-primary)" />
        <div style={{ marginLeft: '4px', fontSize: '12px' }}>谁可以看</div>
      </div>
      <Popup
        title="谁可以看"
        visible={v}
        onMaskClick={() => {
          setV(false);
        }}
        onClose={handleClose}
        bodyStyle={{
          height: 'auto',
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '50vh',
          maxHeight: '80vh',
        }}
        contentStyle={{ padding: '10px  0px' }}
      >
        <div style={{ background: '#fff', minHeight: '100%', padding: '16px' }}>
          {commentAssignerArr.map(item => {
            return (
              <div
                style={{
                  height: '48px',
                  background: '#F8F9FC',
                  marginBottom: '8px',
                  borderRadius: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px',
                }}
              >
                <UserAvatar
                  id={Number(item)}
                  avatarStyle={{ '--border-radius': '50%', '--size': '32px' }}
                />
                <UserName id={Number(item)} style={{ marginLeft: '12px', fontWeight: 400 }} />
              </div>
            );
          })}
        </div>
      </Popup>
    </div>
  );
}
