import React, { useEffect, useState } from 'react';

import { Badge } from '@manyun/base-ui.h5.ui.badge';
import { Block } from '@manyun/base-ui.h5.ui.block';
import { Descriptions } from '@manyun/base-ui.h5.ui.descriptions';
import { Selector } from '@manyun/base-ui.h5.ui.selector';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Tag } from '@manyun/base-ui.h5.ui.tag';
import { Toast } from '@manyun/base-ui.h5.ui.toast';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { useCities } from '@manyun/crm.hook.use-cities';
import { fetchCompassOperateDetail } from '@manyun/crm.service.fetch-compass-operate-detail';
import type { CompassOperateDetailTotals } from '@manyun/crm.service.fetch-compass-operate-detail';
import { useLazyMetadataTree } from '@manyun/resource-hub.gql.client.resources';
import { SpaceText } from '@manyun/resource-hub.h5.ui.space-text';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type OperationDataRecordsProps = { id: string; bizId: string };

function getOperatorTypeInfo(operatorType: string) {
  switch (operatorType) {
    case 'add':
      return { text: '新增', color: 'green' };
    case 'update':
      return { text: '修改', color: 'gold' };
    case 'delete':
      return { text: '删除', color: 'gray' };
    default:
      return undefined;
  }
}

export const OperationDataRecords = ({ id, bizId }: OperationDataRecordsProps) => {
  const { citiesMapper } = useCities();
  const [selectOperatorType, setSelectOperatorType] = useState<string>('all');
  const [compassOperateDetail, setCompassOperateDetail] = useState<CompassOperateDetailTotals>();
  const options = useDeepCompareMemo(() => {
    const defaultOptions = [
      { label: '全部', value: 'all' },
      { label: '新增', value: 'add' },
      { label: '修改', value: 'update' },
      { label: '删除', value: 'delete' },
    ];
    if (compassOperateDetail) {
      defaultOptions[1].label = `新增${compassOperateDetail.addNum}条`;
      defaultOptions[2].label = `修改${compassOperateDetail.updateNum}条`;
      defaultOptions[3].label = `删除${compassOperateDetail.deleteNum}条`;
    }
    return defaultOptions;
  }, [compassOperateDetail]);
  const operationDataRecords = useDeepCompareMemo(() => {
    const list = [...(compassOperateDetail?.totalChangeList ?? [])];
    if (selectOperatorType === 'all') {
      return list;
    } else {
      return list.filter(info => info?.operatorType === selectOperatorType);
    }
  }, [compassOperateDetail, selectOperatorType]);
  const [getMetaData, { data }] = useLazyMetadataTree({
    variables: {
      type: MetaType.CUSTOMER_CODE,
    },
  });

  useEffect(() => {
    getMetaData();
  }, [getMetaData]);

  useEffect(() => {
    if (bizId && id) {
      (async () => {
        const { error, data } = await fetchCompassOperateDetail({ instId: id, dt: bizId });
        if (error) {
          Toast.show({ content: error.message });
          return;
        }
        if (data) {
          setCompassOperateDetail(data);
        }
      })();
    }
  }, [id, bizId]);

  return (
    <Space
      style={{
        width: '100%',
        '--gap-vertical': '8px',
      }}
      direction="vertical"
    >
      <Block radius="x-small" size="middle">
        <Typography.Text style={{ color: 'var(--adm-color-title)', fontSize: 16 }}>
          {`共更正数据 ${compassOperateDetail?.totalNum ?? '--'}条`}
        </Typography.Text>
        <Space style={{ display: 'flex', alignItems: 'center', paddingTop: 4 }}>
          <Typography.Text type="secondary">筛选</Typography.Text>
          <Selector
            style={{
              '--border-radius': '6px',
              '--border': 'solid var(--adm-border-light-color) 1px',
              '--checked-border': 'solid var(--backgroud-color) 1px',
              '--padding': '1px 8px',
              fontSize: 12,
              '--color': 'white',
            }}
            showCheckMark={false}
            options={options}
            value={selectOperatorType ? [selectOperatorType] : undefined}
            onChange={value => {
              setSelectOperatorType(value.length > 0 ? value[0] : 'all');
            }}
          />
        </Space>
      </Block>
      {operationDataRecords.map(
        ({
          id,
          operatorType,
          projectId,
          area,
          blockGuid,
          customerCode,
          ratedPower,
          reciprocateMw,
          reciprocateGrids,
          signedMw,
          signedGrids,
          shelfMw,
          shelfGrids,
          costMw,
          costGrids,
        }) => {
          const operatorTypeInfo = operatorType ? getOperatorTypeInfo(operatorType) : undefined;
          const customer = data?.metadataTree?.find(data => data.code === customerCode)?.name;

          return (
            <Block key={id} radius="x-small" size="middle">
              <Space
                style={{
                  width: '100%',
                  '--gap-vertical': '16px',
                }}
                direction="vertical"
              >
                <Space>
                  {operatorTypeInfo && (
                    <Tag color={operatorTypeInfo.color}>{operatorTypeInfo.text}</Tag>
                  )}
                  {projectId ? (
                    <SpaceText
                      style={{ color: 'var(--adm-color-title)', fontSize: 16 }}
                      spaceGuid={projectId}
                    />
                  ) : (
                    '--'
                  )}
                </Space>
                <div>
                  <Typography.Text>{area ? citiesMapper[area]?.label : '--'}</Typography.Text>
                  <Typography.Text type="secondary"> | </Typography.Text>
                  <Typography.Text>
                    {blockGuid ? `${blockGuid?.split('.')?.[1]}栋` : '--'}
                  </Typography.Text>
                  <Typography.Text type="secondary"> | </Typography.Text>
                  <Typography.Text>{customer ?? '--'}</Typography.Text>
                </div>
                <Space
                  style={{
                    width: '100%',
                  }}
                  direction="vertical"
                >
                  <Space>
                    <Badge
                      style={{
                        marginBottom: '4px',
                        width: '6px',
                        height: '6px',
                        minWidth: '6px',
                        borderRadius: '50%',
                      }}
                      color="var(--adm-color-success)"
                      content={Badge.dot}
                    />
                    <Typography.Text>单机柜规格{ratedPower ?? '--'}KW</Typography.Text>
                  </Space>
                  <Descriptions column={2} colon={false}>
                    {[
                      { label: '交付负载', value: reciprocateMw },
                      { label: '交付机柜数', value: reciprocateGrids },
                      { label: '签约负载', value: signedMw },
                      { label: '签约机柜数', value: signedGrids },
                      { label: '上架负载', value: shelfMw },
                      { label: '上架机柜数', value: shelfGrids },
                      { label: '计费负载', value: costMw },
                      { label: '计费机柜数', value: costGrids },
                    ].map(({ label, value }, index) => (
                      <Descriptions.Item
                        key={label}
                        labelStyle={{ color: 'var(--adm-color-weak)' }}
                        contentStyle={{
                          color: 'var(--adm-color-title)',
                          whiteSpace: 'pre-line',
                        }}
                        label={label}
                      >
                        {`${value ?? '--'}${index % 2 === 0 ? 'MW' : '柜'}`}
                      </Descriptions.Item>
                    ))}
                  </Descriptions>
                </Space>
              </Space>
            </Block>
          );
        }
      )}
    </Space>
  );
};
