import InfoCircleOutlined from '@ant-design/icons/InfoCircleOutlined';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

import { Badge } from '@manyun/base-ui.h5.ui.badge';
import { Block } from '@manyun/base-ui.h5.ui.block';
import { Descriptions } from '@manyun/base-ui.h5.ui.descriptions';
import { Popover } from '@manyun/base-ui.h5.ui.popover';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { fetchEntranceGuardCardAuthGroups } from '@manyun/ticket.service.fetch-entrance-guard-card-auth-groups';
import type { BackendAuthInfo } from '@manyun/ticket.service.fetch-entrance-guard-card-auth-groups';

type AuthInfo = {
  blockTag: string;
  authIdList: number[];
  effectTime: string;
  applyName: string;
  applyId: string;
  entranceCardNo: string;
  dept?: string;
  company?: string;
  phone?: string;
  email?: string;
  cardType?: string;
  cardNo?: string;
  oldEntranceCardNo?: string;
};

type ChangeAuthInfo = {
  originAuthIdList: number[];
  changeType: string;
  oldEffectTime: string;
} & AuthInfo;

type CurrentAuthInfo = Partial<ChangeAuthInfo & { key: string; authInfoList?: AuthInfo[] }>;

export const statusMappings: Record<
  string,
  { status?: 'success' | 'error' | 'default'; text: string; color?: string }
> = {
  ADD: { color: '#2f54eb', text: '新增授权' },
  DELETE: { color: '#f5222d', text: '删除授权' },
  CHANGE: { color: '#13c2c2', text: '变更授权' },
};

export type EntranceGuardCardsProps = {
  idcTag: string;
  data: {
    applyName: string;
    applyId: string;
    entranceCardNo: string;
    dept?: string;
    company?: string;
    phone?: string;
    email?: string;
    cardType?: string;
    cardNo?: string;
    oldEntranceCardNo?: string;
    authInfoList?: AuthInfo[];
    changeAuthInfoList?: ChangeAuthInfo[];
  }[];
  type: 'CARD_CHANGE' | 'CARD_OFF' | 'CARD_EXCHANGE' | 'CARD_APPLY';
};

const EntranceGuardCardTypeText = {
  CARD_CHANGE: '变更信息',
  CARD_OFF: '注销信息',
  CARD_EXCHANGE: '换卡信息',
  CARD_APPLY: '申请人信息',
};

export const EntranceGuardCards = ({ idcTag, data, type }: EntranceGuardCardsProps) => {
  let cardList: CurrentAuthInfo[] = [];
  data.forEach(({ applyName, applyId, authInfoList, changeAuthInfoList, ...rest }) => {
    const info = {
      key: `${applyId}_${applyName}`,
      applyName,
      authInfoList,
      ...rest,
    };
    if (type === 'CARD_CHANGE') {
      cardList = cardList.concat(
        (changeAuthInfoList ?? [])
          .map(item => ({
            ...item,
            ...info,
            effectTime: item.changeType === 'DELETE' ? item.oldEffectTime : item.effectTime,
            authIdList: item.changeType === 'DELETE' ? item.originAuthIdList : item.authIdList,
          }))
          .filter(i => i.changeType !== 'UN_CHANG')
      );
    } else if (type === 'CARD_EXCHANGE') {
      cardList = cardList.concat((authInfoList ?? []).map(item => ({ ...item, ...info })));
    } else {
      cardList = cardList.concat(info);
    }
  });
  if (!data) {
    return null;
  }

  return (
    <Block radius="x-small" size="middle">
      <Space style={{ width: '100%', '--gap-vertical': '12px' }} direction="vertical">
        <Typography.Title style={{ fontSize: 'var(--adm-font-size-8)' }} level={5}>
          {EntranceGuardCardTypeText[type]}
        </Typography.Title>
        <Space style={{ width: '100%', '--gap-vertical': '12px' }} direction="vertical">
          {cardList.map(
            ({
              key,
              applyName,
              entranceCardNo,
              oldEntranceCardNo,
              blockTag,
              authIdList,
              changeType,
              effectTime,
              originAuthIdList,
              phone,
              email,
              company,
              dept,
              cardNo,
              cardType,
              authInfoList,
            }) => {
              if (type === 'CARD_APPLY') {
                return (
                  <Block key={key} style={{ width: '100%', padding: 12 }} radius="x-small" border>
                    <Space style={{ width: '100%', '--gap-vertical': '8px' }} direction="vertical">
                      <Typography.Text style={{ color: 'var(--adm-color-title)' }}>
                        {`${applyName} ${phone}`}
                      </Typography.Text>
                      <Descriptions column={1}>
                        {[
                          { label: '电子邮箱', value: email },
                          { label: '证件编号', value: `${cardType}-${cardNo}` },
                          { label: '所属部门', value: dept },
                          { label: '所属公司', value: company },
                        ].map(({ label, value }) => (
                          <Descriptions.Item
                            key={label}
                            labelStyle={{ color: 'var(--adm-color-weak)', fontSize: 12 }}
                            contentStyle={{
                              color: 'var(--adm-color-title)',
                              whiteSpace: 'pre-line',
                              fontSize: 12,
                            }}
                            label={label}
                          >
                            {value}
                          </Descriptions.Item>
                        ))}
                      </Descriptions>
                      {authInfoList?.map(({ blockTag, authIdList }) => {
                        const blockGuid = `${idcTag}.${blockTag}`;

                        return (
                          <Block
                            key={blockGuid}
                            style={{ width: '100%', padding: '8px 12px' }}
                            color="gray-1"
                            radius="x-small"
                          >
                            <Space>
                              <Badge
                                style={{
                                  width: '6px',
                                  height: '6px',
                                  minWidth: '6px',
                                  borderRadius: '50%',
                                  marginBottom: 4,
                                }}
                                color="var(--adm-color-success)"
                                content={Badge.dot}
                              />
                              <Typography.Text style={{ color: 'var(--adm-color-title)' }}>
                                {`${blockTag}栋`}
                              </Typography.Text>
                              <EntranceGuardCardAuthGroupText
                                code={authIdList?.[0]!}
                                blockGuid={blockGuid}
                              />
                            </Space>
                          </Block>
                        );
                      })}
                    </Space>
                  </Block>
                );
              }

              const blockGuid = `${idcTag}.${blockTag}`;

              return (
                <Block
                  key={key}
                  style={{ width: '100%', padding: 12 }}
                  color="gray-1"
                  radius="x-small"
                >
                  <Space style={{ width: '100%', '--gap-vertical': '0px' }} direction="vertical">
                    <div
                      style={{
                        width: '100%',
                        display: 'flex',
                        justifyContent: 'space-between',
                        paddingBottom: type === 'CARD_CHANGE' ? 8 : 0,
                      }}
                    >
                      <Typography.Text style={{ color: 'var(--adm-color-title)' }}>
                        {applyName}
                      </Typography.Text>
                      <div>
                        <Typography.Text style={{ fontSize: 12 }} type="secondary">
                          {`${type === 'CARD_EXCHANGE' ? '新' : ''}门禁卡编号：`}
                        </Typography.Text>
                        <Typography.Text style={{ color: 'var(--adm-color-title)' }}>
                          {entranceCardNo}
                        </Typography.Text>
                      </div>
                    </div>
                    {type === 'CARD_EXCHANGE' && (
                      <div
                        style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'end',
                        }}
                      >
                        <div>
                          <Typography.Text style={{ fontSize: 12 }} type="secondary">
                            原门禁卡编号：
                          </Typography.Text>
                          <Typography.Text style={{ fontSize: 12 }} type="secondary" delete>
                            {oldEntranceCardNo}
                          </Typography.Text>
                        </div>
                      </div>
                    )}
                    {type !== 'CARD_OFF' && (
                      <div
                        style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <div>
                          <Typography.Text style={{ color: 'var(--adm-color-title)' }}>
                            {`${blockTag}栋`}
                          </Typography.Text>
                          <EntranceGuardCardAuthGroupText
                            code={authIdList?.[0]!}
                            blockGuid={blockGuid}
                          />
                          {changeType === 'CHANGE' && (
                            <Popover
                              placement="top"
                              mode="dark"
                              trigger="click"
                              content={
                                <>
                                  由
                                  <EntranceGuardCardAuthGroupText
                                    blockGuid={blockGuid}
                                    code={originAuthIdList?.[0]!}
                                  />
                                  变更为
                                  <EntranceGuardCardAuthGroupText
                                    blockGuid={blockGuid}
                                    code={authIdList?.[0]!}
                                  />
                                </>
                              }
                            >
                              <InfoCircleOutlined
                                style={{ color: 'var(--adm-color-text)', paddingLeft: 8 }}
                              />
                            </Popover>
                          )}
                        </div>
                        {changeType && (
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            <Badge
                              style={{
                                width: '6px',
                                height: '6px',
                                minWidth: '6px',
                                borderRadius: '50%',
                                marginRight: 8,
                              }}
                              color={statusMappings[changeType]?.color}
                              content={Badge.dot}
                            />
                            {statusMappings[changeType].text}
                          </div>
                        )}
                      </div>
                    )}
                    {type !== 'CARD_OFF' && (
                      <div>
                        <Typography.Text style={{ fontSize: 12 }} type="secondary">
                          授权有效期：
                        </Typography.Text>
                        <Typography.Text style={{ fontSize: 12 }}>
                          {effectTime
                            ? dayjs(effectTime).endOf('D').format('YYYY-MM-DD HH:mm:ss')
                            : '--'}
                        </Typography.Text>
                      </div>
                    )}
                  </Space>
                </Block>
              );
            }
          )}
        </Space>
      </Space>
    </Block>
  );
};

export type EntranceGuardCardAuthGroupTextProps = {
  code: number;
  blockGuid: string;
};
export function EntranceGuardCardAuthGroupText({
  code,
  blockGuid,
}: EntranceGuardCardAuthGroupTextProps) {
  const [authGroupMappings, setAuthGroupMappings] = useState<Record<number, BackendAuthInfo>>({});

  useEffect(() => {
    (async () => {
      const { data, error } = await fetchEntranceGuardCardAuthGroups({ blockGuid });
      if (error) {
        return;
      }
      if (data) {
        const authMappings: Record<number, BackendAuthInfo> = {};
        data.data.forEach((item: BackendAuthInfo) => {
          authMappings[item.authId] = item;
        });
        setAuthGroupMappings(authMappings);
      }
    })();
  }, [blockGuid, code]);
  return (
    <Typography.Text style={{ fontSize: 12, paddingLeft: 8 }}>
      {authGroupMappings[code]?.authName}
    </Typography.Text>
  );
}
