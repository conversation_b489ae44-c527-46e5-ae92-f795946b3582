import { useCallback } from 'react';

import { setUserInfo } from '@manyun/auth-hub.cache.user';
import { LoginType } from '@manyun/auth-hub.service.login';
import { useClientLogger } from '@manyun/dc-brain.gql.client.client-logs';
import { WebsiteCode } from '@manyun/dc-brain.model.website';
import { useLogin } from '@manyun/iam.gql.client.iam';
import type { MutationLoginArgs } from '@manyun/iam.gql.client.iam';

export type LoginUser = {
  id: number;
  userName: string;
  loginName: string;
  mobile: string | null;
  company: string | null | undefined;
  roleCode: string | null;
};

const globalNavigator = window.navigator as Navigator & {
  userAgentData?: { toJSON(): void };
};

export const useH5Login = () => {
  const [login, { loading, data }] = useLogin();
  const [remoteLog] = useClientLogger();

  const writeRemoteLog = useCallback(
    (loginUser: Pick<LoginUser, 'id' | 'loginName' | 'userName'>) => {
      remoteLog({
        variables: {
          type: 'info',
          message: 'User logged in from H5/DC Base',
          metas: [
            JSON.stringify({
              user: {
                id: loginUser.id,
                login: loginUser.loginName,
                name: loginUser.userName,
              },
              client: {
                ua: globalNavigator.userAgent,
                uaData:
                  globalNavigator.userAgentData && typeof globalNavigator.userAgentData == 'object'
                    ? globalNavigator.userAgentData.toJSON()
                    : undefined,
                windowSize: {
                  width: window.innerWidth,
                  height: window.innerHeight,
                  zoom: window.devicePixelRatio,
                },
                visualViewport: window.visualViewport
                  ? {
                      width: window.visualViewport.width,
                      height: window.visualViewport.height,
                      scale: window.visualViewport.scale,
                    }
                  : undefined,
              },
            }),
          ],
        },
      });
    },
    [remoteLog]
  );

  const onLoginInSuccess = useCallback(
    ({
      user,
      expiredAt,
      loginClient,
      serviceTicket,
      callback,
    }: {
      user: LoginUser;
      /**
       * 过期时间
       */
      expiredAt: number;
      serviceTicket: string;
      loginClient: string;
      callback: (data: LoginUser) => void;
    }) => {
      writeRemoteLog({
        id: user.id,
        userName: user.userName,
        loginName: user.loginName,
      });
      if (serviceTicket) {
        localStorage.setItem('serviceTicket', serviceTicket);
      }
      if (loginClient) {
        localStorage.setItem('loginClient', loginClient);
      }
      if (user.roleCode) {
        localStorage.setItem('roleCode', user.roleCode);
      }
      setUserInfo({
        id: user!.id,
        login: user!.loginName,
        name: user!.userName,
        mobileNumber: user?.mobile ?? '',
        company: user!.company,
        needResetPassword: false,
        expiredTime: expiredAt,
        type: LoginType.TICKET,
      });
      callback(user);
    },
    [writeRemoteLog]
  );

  const executeLoin = async ({
    variables,
    onFail,
    onSuccess,
  }: {
    variables: MutationLoginArgs;
    onFail: (message: string) => void;
    onSuccess: (data: LoginUser) => void;
  }) => {
    const { data, errors } = await login({
      variables: { ...variables, website: 'ALL' },
    });

    if (errors) {
      onFail(errors[0].message);
      return;
    }

    if (data && data.login && data.login.success) {
      const _user = data.login?.user;
      const _userOwnerSite = WebsiteCode.DcBase;
      //确定用户当前所属站点
      if (_userOwnerSite === WebsiteCode.DcBase && _user) {
        onLoginInSuccess({
          user: {
            id: _user.id,
            userName: _user.userName,
            loginName: _user.loginName,
            mobile: _user.mobile ?? '',
            company: _user.company,
            roleCode: _user.roleCode ?? null,
          },
          loginClient: variables.client!,
          expiredAt: data.login?.expiredAt!,
          serviceTicket: data.login?.sessionId!,
          callback: onSuccess,
        });
        return;
      }
    }
  };

  return [executeLoin, { loading, data }] as const;
};
