import EyeInvisibleOutlined from '@ant-design/icons/EyeInvisibleOutlined';
import EyeOutlined from '@ant-design/icons/EyeOutlined';
import { useNavigate } from '@tanstack/react-location';
import React, { useMemo, useState } from 'react';

import { LoginClient, LoginType } from '@manyun/auth-hub.service.login';
import { Button } from '@manyun/base-ui.h5.ui.button';
import { Form } from '@manyun/base-ui.h5.ui.form';
import { Input } from '@manyun/base-ui.h5.ui.input';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Tabs } from '@manyun/base-ui.h5.ui.tabs';
import { Toast } from '@manyun/base-ui.h5.ui.toast';
import { HOME_ROUTE_PATH } from '@manyun/dc-brain.h5.route.routes';
import { LoginStatus, useStoreContext } from '@manyun/dc-brain.h5.store.store';
import { useH5Login } from '@manyun/iam.h5.hook.use-login';

/** 账号密码登录 */
export function DevelopLogin() {
  const navigate = useNavigate();
  const [, { setUser }] = useStoreContext();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loginType, setLoginType] = useState<LoginType>(LoginType.DEFAULT);

  const [h5Login] = useH5Login();

  const onFinish = async ({ name, password }: { name: string; password: string }) => {
    setLoading(true);
    h5Login({
      variables: { type: loginType, login: name, client: LoginClient.H5, password },
      onFail: message => {
        setLoading(false);
        Toast.show({
          content: `登录失败：${message} `,
          duration: 2000,
        });
      },
      onSuccess: user => {
        setLoading(false);
        setUser({
          userId: user.id,
          userName: user.loginName,
          name: user.userName,
          userState: LoginStatus.LOGGED_IN,
          company: user.company ?? null,
          roleCode: user.roleCode,
        });
        Toast.show('登录成功');
        navigate({ to: HOME_ROUTE_PATH });
      },
    }).finally(() => {
      setLoading(false);
    });
  };

  const loginNameText = useMemo(
    () => (loginType === LoginType.DEFAULT ? '用户名' : '域账号'),
    [loginType]
  );

  return (
    <Space
      style={{
        background: '#fff',
        '--gap-horizontal': '0',
        width: '100vw',
        height: '100vh',
        '--gap-vertical': '16px',
        padding: '24px 0',
      }}
      direction="vertical"
    >
      <Tabs onChange={value => setLoginType(value as LoginType)}>
        {[
          {
            key: LoginType.DEFAULT,
            title: '账号密码登录',
          },
          {
            key: LoginType.LDAP,
            title: '企业域名登录',
          },
        ].map(item => (
          <Tabs.Tab key={item.key} title={item.title} />
        ))}
      </Tabs>
      <Form
        style={{ padding: 0 }}
        layout="horizontal"
        footer={
          <Button style={{ marginTop: 16 }} block type="submit" color="primary" loading={loading}>
            登录
          </Button>
        }
        onFinish={onFinish}
      >
        <Form.Item
          name="name"
          label={loginNameText}
          rules={[
            {
              required: true,
              message: `${loginNameText}不能为空`,
            },
          ]}
          space={false}
          divider
        >
          <Input
            placeholder={`请输入${loginNameText}
            `}
          />
        </Form.Item>
        <Form.Item
          name="password"
          label="密码"
          rules={[{ required: true, message: '密码不能为空' }]}
          extra={
            <div>
              {!visible ? (
                <EyeInvisibleOutlined onClick={() => setVisible(true)} />
              ) : (
                <EyeOutlined onClick={() => setVisible(false)} />
              )}
            </div>
          }
          space={false}
          divider
        >
          <Input placeholder="请输入密码" type={visible ? 'text' : 'password'} />
        </Form.Item>
      </Form>
    </Space>
  );
}
