import React, { useRef, useState } from 'react';

import type { TextAreaRef } from 'antd-mobile';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { Divider } from '@manyun/base-ui.h5.ui.divider';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { TextArea, type TextAreaProps } from '@manyun/base-ui.h5.ui.textarea';
import { Typography } from '@manyun/base-ui.h5.ui.typography';

import { ReferToPicker } from '@manyun/hrm.h5.ui.refer-to-picker';

export type UserMentionsProps = Omit<TextAreaProps, 'onClick'> & {
  onPackUp?: (packUp: boolean) => void;
  onClick: (content: string, user?: { id: number | string; name: string }) => void;
};

export function UserMentions({
  value,
  placeholder = '可@相关人员',
  onPackUp,
  onClick,
  ...resp
}: UserMentionsProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const selectionStartRef = useRef({
    start: 0,
    end: 0,
  });

  const textAreaRef = useRef<TextAreaRef | null>(null);

  const getPositionForTextArea = () => {
    if (!textAreaRef?.current) {
      return;
    }
    if (textAreaRef.current.nativeElement?.selectionStart) {
      selectionStartRef.current.start = textAreaRef.current.nativeElement.selectionStart;
    }
    if (textAreaRef.current.nativeElement?.selectionEnd) {
      selectionStartRef.current.end = textAreaRef.current.nativeElement.selectionEnd;
    }
  };

  return (
    <>
      <TextArea
        ref={textAreaRef}
        // autoSize={{ minRows: 8, maxRows: 12 }}
        value={value}
        rows={6}
        placeholder={placeholder}
        {...resp}
        onChange={text => {
          getPositionForTextArea();
          if (
            text.length > (value?.length || 0) &&
            selectionStartRef.current.end === selectionStartRef.current.start &&
            text.charCodeAt(selectionStartRef.current.end - 1) === 64
          ) {
            textAreaRef.current?.blur();
            setVisible(true);
          }
          onClick(text);
        }}
      />
      <Divider style={{ margin: '8px 0' }} />
      <Typography.Text
        style={{ fontSize: 'calc(var(--adm-font-size-main) + 1px)' }}
        type="secondary"
        onClick={() => {
          getPositionForTextArea();
          textAreaRef.current?.blur();
          setVisible(true);
        }}
      >
        @相关人员
      </Typography.Text>
      <Popup
        visible={visible}
        bodyStyle={{
          maxHeight: '80vh',
          minHeight: 400,
          overflowY: 'auto',
        }}
        destroyOnClose
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <Block style={{ marginTop: 12, paddingTop: 0 }}>
          <ReferToPicker
            filterCurrentUser={false}
            onClick={(id, name) => {
              let _content = `${value}@${name}`;
              if (value?.length === selectionStartRef.current.end) {
                if (value && value.charCodeAt(selectionStartRef.current.end - 1) === 64) {
                  _content = `${value}${name}`;
                }
              } else {
                const startStr = value?.substring(0, selectionStartRef.current.end);
                const endStr = value?.substring(selectionStartRef.current.end, value.length);
                if (value && value.charCodeAt(selectionStartRef.current.end - 1) === 64) {
                  _content = startStr + name + endStr;
                } else {
                  _content = startStr + '@' + name + endStr;
                }
              }
              textAreaRef.current?.focus();
              onClick(_content, { id, name });
              setVisible(false);
            }}
          />
        </Block>
      </Popup>
    </>
  );
}
