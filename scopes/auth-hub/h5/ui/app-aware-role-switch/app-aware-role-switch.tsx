import React, { useEffect, useMemo } from 'react';

import { UserSwitchOutlined } from '@ant-design/icons';
import { Toast } from 'antd-mobile';

import { Popover } from '@manyun/base-ui.h5.ui.popover';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Tag } from '@manyun/base-ui.h5.ui.tag';
import { Typography } from '@manyun/base-ui.h5.ui.typography';

import { useAppIdentity, useLazyAppAwareRoles, useSwitchRole } from '@manyun/iam.gql.client.iam';

export type AppAwareRoleSwitchProps = {
  className?: string;
};

const RoleSplitSymbol = '_$$_';

export function AppAwareRoleSwitch({ className }: AppAwareRoleSwitchProps) {
  const { data: appIdentityData } = useAppIdentity({ fetchPolicy: 'no-cache' });

  const [fetchAppAwareRoles, { data }] = useLazyAppAwareRoles();
  const [saveLatestUsedRole] = useSwitchRole({
    fetchPolicy: 'network-only',
  });

  const selectedRoleCode = useMemo(() => {
    return appIdentityData?.appIdentity?.roleCode;
  }, [appIdentityData?.appIdentity?.roleCode]);

  useEffect(() => {
    if (appIdentityData?.appIdentity?.site) {
      fetchAppAwareRoles({
        variables: {
          website: appIdentityData.appIdentity.site,
        },
        fetchPolicy: 'network-only',
      });
    }
  }, [appIdentityData?.appIdentity?.site, fetchAppAwareRoles]);

  useEffect(() => {
    const tempRoleName = localStorage.getItem('tempRoleName');
    if (tempRoleName) {
      setTimeout(() => {
        Toast.show(`已切换角色为${tempRoleName}`);
        localStorage.removeItem('tempRoleName');
      }, 300);
    }
  }, []);

  const actions = useMemo(() => {
    return (data?.appAwareRoles ?? []).map(role => ({
      key: `${role.id}${RoleSplitSymbol}${role.code}${RoleSplitSymbol}${role.name}`,
      text: (
        <Space
          style={{
            fontSize: 14,
            '--gap-horizontal': '24px',
          }}
          align="center"
          justify="center"
        >
          {role.name}
          {selectedRoleCode === role.code && <Tag color="green">当前</Tag>}
        </Space>
      ),
    }));
  }, [data?.appAwareRoles, selectedRoleCode]);

  return (
    <div
      style={{
        width: '100%',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
        <Typography.Text style={{ fontSize: 12, maxWidth: 'calc(100vw - 210px)' }}>
          {(data?.appAwareRoles ?? []).find(role => role.code === selectedRoleCode)?.name ?? ''}
        </Typography.Text>
        <Tag color="gray">当前角色</Tag>
      </div>
      {(data?.appAwareRoles ?? []).length > 1 && (
        <Popover.Menu
          actions={actions}
          placement="bottom-end"
          trigger="click"
          onAction={node => {
            if (node.key !== 'user') {
              const role = (node.key as string)?.split(RoleSplitSymbol);
              const roleCode = role[1];
              const roleName = role[2];
              localStorage.setItem('roleCode', roleCode);
              localStorage.setItem('tempRoleName', roleName);
              saveLatestUsedRole({
                variables: {
                  code: roleCode,
                },
                onCompleted() {
                  window.location.reload();
                },
              });
            }
          }}
        >
          <Space
            style={{ color: 'var(--adm-color-primary)', '--gap': '4px', fontSize: 12 }}
            align="center"
            justify="center"
          >
            <UserSwitchOutlined style={{ fontSize: 12 }} className={className} />
            切换角色
          </Space>
        </Popover.Menu>
      )}
    </div>
  );
}
