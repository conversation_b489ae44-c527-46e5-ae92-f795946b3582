import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import type { CheckListProps } from '@manyun/base-ui.h5.ui.check-list';
import { CheckList } from '@manyun/base-ui.h5.ui.check-list';
import { Empty } from '@manyun/base-ui.h5.ui.empty';
import { Loading } from '@manyun/base-ui.h5.ui.loading';
import type { PopupRef } from '@manyun/base-ui.h5.ui.popup';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { SearchBar } from '@manyun/base-ui.h5.ui.search-bar';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Typography } from '@manyun/base-ui.h5.ui.typography';

import type { Role } from '@manyun/auth-hub.model.role';
import { useIntl } from '@manyun/dc-brain.h5.store.store';
import { useLazyRoles } from '@manyun/iam.gql.client.iam';

export type LabelInValue = {
  label: string;
  value: string;
} & Role;

export type RolesCheckListPickerProps = Omit<CheckListProps, 'value' | 'onChange'> & {
  title?: string;
  visible?: boolean;
  showSearch?: boolean;
  showLabel?: boolean;
  authorized?: boolean;
  labelInValue?: boolean;
  value?: string[] | LabelInValue[];
  multiple?: boolean;
  /**
   * 指定需筛选的资源类型
   */
  resourceTypes?: string[];
  onChange?: (value: string[] | LabelInValue[]) => void;
};

export const RolesCheckListPicker = React.forwardRef<PopupRef, RolesCheckListPickerProps>(
  (
    {
      showSearch = true,
      value,
      visible,
      title,
      labelInValue = true,
      authorized = false,
      multiple = false,
      showLabel = true,
      resourceTypes,
      onChange,
      ...restProps
    },
    ref
  ) => {
    const [openPicker, setOpenPicker] = useState(false);
    const innerRef = useRef<PopupRef | null>(null);
    const intl = useIntl();
    const [searchText, setSearchText] = useState<string>();
    const [innerValue, setInnerValue] = useState<string[] | LabelInValue[]>(value ?? []);
    const [getRoles, { data, loading }] = useLazyRoles();

    const actions: PopupRef = {
      toggle: () => {
        innerRef.current?.toggle();
      },
      open: () => {
        setOpenPicker(true);
        innerRef.current?.open();
      },
      close: () => {
        setOpenPicker(false);
        innerRef.current?.close();
      },
    };

    useImperativeHandle(ref, () => actions);

    const options: LabelInValue[] = useMemo(
      () =>
        (data?.roles_next ?? []).map(
          role =>
            ({
              ...role,
              label: role.name!,
              value: role.code!,
            } as LabelInValue)
        ),
      [data?.roles_next]
    );

    useEffect(() => {
      getRoles({
        variables: {
          name: searchText,
          resourceTypes: resourceTypes,
        },
      });
    }, [getRoles, resourceTypes, searchText]);

    return (
      <>
        <Popup
          ref={innerRef}
          visible={openPicker ?? visible}
          title={title}
          bodyStyle={{
            overflowY: 'auto',
            height: '80vh',
          }}
          contentStyle={{ padding: '10px  0px 0px' }}
          footer={multiple}
          arrow={false}
          onMaskClick={() => {
            setOpenPicker(false);
          }}
          onOK={() => {
            onChange?.(innerValue);
            setOpenPicker(false);
          }}
        >
          <Block style={{ padding: 0, overflowY: 'auto', height: '100%' }}>
            {showSearch && (
              <Block>
                <SearchBar
                  placeholder="请输入角色名称查询"
                  defaultValue={searchText}
                  onSearch={val => {
                    setSearchText(val);
                  }}
                  onClear={() => {
                    setSearchText(undefined);
                  }}
                />
              </Block>
            )}
            {loading && <Loading fullPage />}
            {options.length === 0 ? (
              <Empty description="暂无数据" />
            ) : (
              <CheckList
                {...restProps}
                multiple={multiple}
                value={
                  multiple
                    ? innerValue.map(value => (typeof value === 'object' ? value.value : value))
                    : value?.map(value => (typeof value === 'object' ? value.value : value))
                }
                onChange={value => {
                  if (multiple) {
                    setInnerValue(
                      labelInValue ? options.filter(opt => value.includes(opt.value)) : value
                    );
                  } else {
                    onChange?.(
                      labelInValue ? options.filter(opt => value.includes(opt.value)) : value
                    );
                    setOpenPicker(false);
                  }
                }}
              >
                {options
                  // .filter(opt =>
                  //   searchText
                  //     ? opt.label.toLocaleLowerCase().indexOf(searchText.toLocaleLowerCase()) > -1
                  //     : true
                  // )
                  .map(({ label, value }) => (
                    <CheckList.Item key={value} value={value}>
                      {label}
                    </CheckList.Item>
                  ))}
              </CheckList>
            )}
          </Block>
        </Popup>
        {showLabel && (
          <Space
            style={{ width: '85vw' }}
            onClick={() => {
              setOpenPicker(true);
              setInnerValue(value ?? []);
            }}
          >
            {value && value.length > 0 ? (
              <Typography.Text style={{ maxWidth: '90vw' }} ellipsis>
                {value.map(value => (typeof value === 'object' ? value.label : value)).join('｜')}
              </Typography.Text>
            ) : (
              <span style={{ color: 'var(--adm-color-light)' }}>
                {intl.getMessage('common.placeholder', '请选择')}
              </span>
            )}
          </Space>
        )}
      </>
    );
  }
);

RolesCheckListPicker.displayName = 'RolesCheckListPicker';
