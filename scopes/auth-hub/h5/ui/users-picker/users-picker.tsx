import CloseOutlined from '@ant-design/icons/CloseOutlined';
import PlusOutlined from '@ant-design/icons/PlusOutlined';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.h5.ui.button';
import { Space } from '@manyun/base-ui.h5.ui.space';
import type { ReferToPickerProps } from '@manyun/hrm.h5.ui.refer-to-picker';
import { UserAvatar } from '@manyun/iam.h5.ui.user';
import { UserPicker } from '@manyun/iam.h5.ui.user-picker';

export type User = {
  id: number;
  name: string;
};

export type UsersPickerProps = {
  value: User[];
  onChange: (value: User[]) => void;
} & Omit<ReferToPickerProps, 'onClick'>;

export function UsersPicker({ value, onChange, ...restPorps }: UsersPickerProps) {
  const [pickerVisible, setPickerVisible] = useState(false);

  return (
    <>
      <Space wrap>
        <Button
          size="middle"
          color="primary"
          fill="none"
          shape="rounded"
          style={{ background: 'var(--backgroud-color)', height: '32px' }}
          onClick={() => {
            setPickerVisible(!pickerVisible);
          }}
        >
          <PlusOutlined style={{ marginRight: '4px' }} />
          添加
        </Button>
        {value?.map(user => (
          <Space
            key={user.id}
            style={{
              background: 'var(--backgroud-color)',
              padding: '5px 4px',
              '--gap': '0',
              borderRadius: 'var(--border-radius,22px)',
            }}
            align="center"
            justify="center"
          >
            <Space align="center" justify="center">
              <UserAvatar avatarStyle={{ '--size': '24px', borderRadius: '50%' }} id={user.id} />
              {user.name}
              <CloseOutlined
                style={{ width: '12px', height: '12px', color: '#bbb', marginRight: '8px' }}
                onClick={() => {
                  onChange(value.filter(_user => _user.id !== user.id));
                }}
              />
            </Space>
          </Space>
        ))}
      </Space>
      <UserPicker
        showLabel={false}
        visible={pickerVisible}
        approvalUserIds={restPorps?.approvalUserIds}
        onChange={val => {
          const newValue = [...value];
          if (!newValue.map(val => val.id).includes(Number(val.value))) {
            newValue.push({ id: Number(val.value), name: val.label });
          }
          onChange(newValue);
          setPickerVisible(false);
        }}
        onMaskClick={() => {
          setPickerVisible(false);
        }}
        onClickArrow={() => {
          setPickerVisible(false);
        }}
      />
    </>
  );
}
