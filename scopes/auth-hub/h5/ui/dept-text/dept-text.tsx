import React, { useCallback, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Toast } from 'antd-mobile';

import type { User } from '@manyun/auth-hub.model.user';
import { fetchDeptDetail } from '@manyun/auth-hub.service.fetch-dept-detail';
import { fetchUser } from '@manyun/auth-hub.service.fetch-user';

const BLANK_PLACEHOLDER = '--';

export type DeptTextProps = {
  /** 是否自 MDM 同步的数据 */
  fromMdm?: boolean;
} & (
  | {
      userId: User['id'];
    }
  | {
      deptId: User['departmentId'];
    }
);

export function DeptText({ fromMdm = true, ...props }: DeptTextProps) {
  const [text, setText] = useState<string>(BLANK_PLACEHOLDER);
  const [useFormMdm, setUserFromMdm] = useState(fromMdm);

  const fetchDept = useCallback(
    async (departmentId: string) => {
      const { error, data } = await fetchDeptDetail({ deptId: departmentId });
      if (error) {
        Toast.show(error.message);
        return;
      }
      if (data) {
        if (useFormMdm) {
          // 产品要求：同步数据则排除第一个部门（即数据中心）
          setText(data.fullDeptName.split('-').slice(1).join('-'));
        } else {
          setText(data.nameZh);
        }
      }
    },
    [useFormMdm]
  );

  useShallowCompareEffect(() => {
    if ('userId' in props && props.userId) {
      (async () => {
        const { error, data } = await fetchUser({ id: props.userId });
        if (error) {
          Toast.show(error.message);
          return;
        }
        if (data && data.departmentId) {
          setUserFromMdm(data.fromMdm);
          fetchDept(data.departmentId);
        }
      })();
    } else if ('deptId' in props && props.deptId) {
      fetchDept(props.deptId);
    }
  }, [fetchDept, fromMdm, props]);

  return <>{text || BLANK_PLACEHOLDER}</>;
}
