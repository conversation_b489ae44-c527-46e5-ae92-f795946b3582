import { Toast } from 'antd-mobile';
import React, { useCallback, useEffect, useState } from 'react';

import type { UserJSON } from '@manyun/auth-hub.model.user';
import { fetchUser } from '@manyun/auth-hub.service.fetch-user';
import { Avatar } from '@manyun/base-ui.h5.ui.avatar';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { useStoreContext } from '@manyun/dc-brain.h5.store.store';
import { TENANT_ID_IS_YG } from '@manyun/dc-brain.h5.util.common';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';

import Logo from './logo.png';

export type UserProps = {
  id: number;
  avatarStyle?: React.CSSProperties & {
    '--size'?: string;
    '--border-radius'?: string;
  };
  disabled?: boolean;
};

export function useUser(id: number, shouldFetch = true) {
  const [user, setUser] = useState<UserJSON>();

  useEffect(() => {
    if (shouldFetch && id) {
      (async () => {
        const { error, data } = await fetchUser({ id });
        if (error) {
          Toast.show({ content: error.message });
          return;
        }
        if (data) {
          setUser(data);
        }
      })();
    }
  }, [id, shouldFetch]);

  return [user] as const;
}

export function User({ id, avatarStyle }: UserProps) {
  return (
    <>
      <UserAvatar id={id} avatarStyle={avatarStyle} />
    </>
  );
}

export function UserAvatar({
  id,
  avatarStyle,
  disabled,
}: Pick<UserProps, 'id' | 'avatarStyle' | 'disabled'>) {
  const [avatarUrl, setAvatarUrl] = useState('');
  const [user] = useUser(id);
  const [, { setUser }] = useStoreContext();

  useEffect(() => {
    setUser(prev => ({ ...prev, gender: user?.gender }));
  }, [setUser, user?.gender]);

  const getUserAvater = useCallback(async () => {
    const { error, data } = await fetchBizFileInfos({
      targetId: id.toString(),
      targetType: 'USER',
    });
    if (error) {
      Toast.show({ content: error.message });
      return;
    }
    if (data.data.length > 0) {
      setAvatarUrl(data.data[0].src);
    }
  }, [id]);

  useEffect(() => {
    getUserAvater();

    //在阳高环境，字节的人用字节logo
    if (TENANT_ID_IS_YG && Number(id) === -1) {
      setAvatarUrl(Logo);
    }
  }, []);

  if (user === null) {
    return <span>Loading...</span>;
  }

  // 使用用户名的最后两个字符作为头像显示
  const firstLetter = (user?.name || '').slice(-2) || '未';

  return (
    <Avatar
      src={avatarUrl}
      fallback={
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: 'inherit',
            width: '100%',
            color: 'white',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            transform: 'scale(0.8)', // 将 10px 的字体缩放到视觉上的 8px
            transformOrigin: 'center center',
          }}
        >
          {firstLetter}
        </div>
      }
      style={{
        backgroundColor: disabled ? '#BFBFBF' : 'var(--adm-color-primary)',
        '--size': '20px',
        borderRadius: '50%',
        fontSize: '10px',
        ...avatarStyle,
      }}
    />
  );
}

export type UserNameProps = {
  id: number;
  variant?: 'title' | 'text';
  name?: string;
  style?: React.CSSProperties;
};

export function UserName({ id, variant = 'title', name, style }: UserNameProps) {
  const [user] = useUser(id, name === undefined);

  const Warpper = variant === 'title' ? Typography.Title : Typography.Text;

  return (
    <Warpper level={4} style={{ margin: 0, ...style }}>
      {name || user?.name || '未知'}
    </Warpper>
  );
}

export type UserJobTitleProps = {
  id: number;
  jobTitle?: string;
  style?: React.CSSProperties;
};

export function UserJobTitle({ id, jobTitle, style }: UserJobTitleProps) {
  const [user] = useUser(id, jobTitle === undefined);

  return (
    <Typography.Title level={4} style={{ margin: 0, ...style }}>
      {jobTitle || user?.title}
    </Typography.Title>
  );
}
