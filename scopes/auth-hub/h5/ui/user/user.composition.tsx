import React from 'react';

import { useRemoteMock as remoteMock } from '@manyun/service.request';

import { UserAvatar, UserJobTitle, UserName } from './user';

export const BasicUserAvatar = () => {
  const [initialized, setInitilized] = React.useState(false);

  React.useEffect(() => {
    const mockOff = remoteMock('web');
    setInitilized(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return <UserAvatar id={1} />;
};

export const BasicUserName = () => {
  const [initialized, setInitilized] = React.useState(false);

  React.useEffect(() => {
    const mockOff = remoteMock('web');
    setInitilized(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return <UserName id={1} />;
};

export const BasicUserJobTitle = () => {
  const [initialized, setInitilized] = React.useState(false);

  React.useEffect(() => {
    const mockOff = remoteMock('web');
    setInitilized(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return <UserJobTitle id={1} />;
};
