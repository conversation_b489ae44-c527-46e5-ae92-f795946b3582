import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.h5.ui.button';

import { UserPicker } from './user-picker';

export const BasicUserPicker = () => {
  const [visible, setVisible] = useState(false);
  return (
    <>
      <Button
        onClick={() => {
          setVisible(true);
        }}
      >
        选择用户
      </Button>
      <UserPicker visible={visible} />
    </>
  );
};
