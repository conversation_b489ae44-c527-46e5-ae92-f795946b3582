import React, { useImperativeHandle, useRef } from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import type { PopupRef } from '@manyun/base-ui.h5.ui.popup';
import { useIntl } from '@manyun/dc-brain.h5.store.store';
import type { ReferToPickerProps } from '@manyun/hrm.h5.ui.refer-to-picker';
import { ReferToPicker } from '@manyun/hrm.h5.ui.refer-to-picker';

export type LabelInValue = {
  label: string;
  value: string;
};

export type UserPickerProps = {
  title?: string;
  value?: LabelInValue;
  onChange?: (value: LabelInValue) => void;
  visible?: boolean;
  onMaskClick?: () => void;
  onClickArrow?: () => void;
  showLabel?: boolean;
} & Omit<ReferToPickerProps, 'onClick'>;

export const UserPicker = React.forwardRef<PopupRef, UserPickerProps>(
  (
    {
      title = '请选择人员',
      onChange,
      value,
      visible,
      showLabel = true,
      onMaskClick,
      onClickArrow,
      ...restPorps
    },
    ref
  ) => {
    const innerRef = useRef<PopupRef | null>(null);
    const intl = useIntl();

    const actions: PopupRef = {
      toggle: () => {
        innerRef.current?.toggle();
      },
      open: () => {
        innerRef.current?.open();
      },
      close: () => {
        innerRef.current?.close();
      },
    };

    useImperativeHandle(ref, () => actions);

    return (
      <>
        <Popup
          ref={innerRef}
          visible={visible}
          title={title}
          bodyStyle={{
            minHeight: '80vh',
            overflowY: 'auto',
          }}
          destroyOnClose
          footer={false}
          arrow
          onMaskClick={() => {
            onMaskClick?.();
          }}
          onClickArrow={onClickArrow}
        >
          <Block style={{ marginTop: 12, paddingTop: 0, overflowY: 'auto' }}>
            <ReferToPicker
              resourceCode={restPorps.resourceCode}
              filterCurrentUser={restPorps.filterCurrentUser}
              applyUserId={restPorps.applyUserId}
              approvalUserIds={restPorps.approvalUserIds}
              onClick={(id, name) => {
                onChange?.({ value: id.toString(), label: name });
                innerRef.current?.close();
              }}
            />
          </Block>
        </Popup>
        {showLabel && (
          <>
            {value ? (
              value.label
            ) : (
              <span style={{ color: 'var(--adm-color-light)' }}>
                {intl.getMessage('common.placeholder', '请选择')}
              </span>
            )}
          </>
        )}
      </>
    );
  }
);

UserPicker.displayName = 'UserPicker';
