import React from 'react';

import { destroyMock, webRequest } from '@manyun/service.request';

import { RoleText } from './role-text';

export const BasicRoleText = () => {
  const [initialized, update] = React.useState(false);
  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
    return () => {
      // mockOff();
    };
  }, []);

  if (!initialized) {
    return null;
  }
  return <RoleText codes={['1', 'ADMIN', '3']} />;
};
