import React, { useEffect } from 'react';

import { Loading } from '@manyun/base-ui.h5.ui.loading';

import { useLazyRoles } from '@manyun/iam.gql.client.iam';

export type RoleTextProps = {
  codes: string[];
};

export function RoleText({ codes }: RoleTextProps) {
  const [getRoles, { data, loading }] = useLazyRoles();

  const selectRolesName = (codes: string[]) => {
    const roleNames: string[] = [];
    codes.forEach(code => {
      const role = (data?.roles_next ?? []).find(_role => _role.code === code);
      if (role) {
        roleNames.push(role.name);
      }
    });
    return roleNames;
  };

  useEffect(() => {
    getRoles();
  }, [getRoles]);

  if (loading) {
    return <Loading />;
  }

  return <>{selectRolesName(codes).join('｜')}</>;
}
