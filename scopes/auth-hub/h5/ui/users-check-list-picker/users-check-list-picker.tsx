import React, { useImperativeHandle, useRef, useState } from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { CheckList } from '@manyun/base-ui.h5.ui.check-list';
import { Empty } from '@manyun/base-ui.h5.ui.empty';
import { Loading } from '@manyun/base-ui.h5.ui.loading';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import type { PopupProps, PopupRef } from '@manyun/base-ui.h5.ui.popup';
import { SearchBar } from '@manyun/base-ui.h5.ui.search-bar';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Typography } from '@manyun/base-ui.h5.ui.typography';

import { useIntl, useStoreContext } from '@manyun/dc-brain.h5.store.store';
import { useLazyGetUsersForUserSelect } from '@manyun/iam.gql.client.users';

import { useLazyUsersByResourceCode } from './use-users-by-resource-codes';

export type LabelInValue = {
  name: string;
  id: string;
  email?: string;
};

export type UsersCheckListPickerProps = {
  idc?: string;
  blockGuidList?: string[];
  title?: string;
  value?: LabelInValue[];
  visible?: boolean;
  showLabel?: boolean;
  /**
   * 是否包含当前登陆人
   */
  includeCurrentUser?: boolean;
  /**
   * 是否支持多选
   */
  multiple?: boolean;
  showUserEmail?: boolean;
  onChange?: (value: LabelInValue[]) => void;
  onMaskClick?: () => void;
} & Omit<PopupProps, 'onClick'>;

export const UsersCheckListPicker = React.forwardRef<PopupRef, UsersCheckListPickerProps>(
  (
    {
      idc,
      blockGuidList,
      title,
      value,
      visible,
      multiple = false,
      showLabel = true,
      showUserEmail = false,
      includeCurrentUser = true,
      onMaskClick,
      onChange,
    },
    ref
  ) => {
    const [{ user }] = useStoreContext();
    const [openPicker, setOpenPicker] = useState(false);
    const [searchValue, setSearchValue] = useState<string>('');
    const [innerValues, setInnerValues] = useState<LabelInValue[]>(value ?? []);

    const innerRef = useRef<PopupRef | null>(null);
    const intl = useIntl();

    const actions: PopupRef = {
      toggle: () => {
        innerRef.current?.toggle();
      },
      open: () => {
        setOpenPicker(true);
        innerRef.current?.open();
      },
      close: () => {
        setOpenPicker(false);
        innerRef.current?.close();
      },
    };

    useImperativeHandle(ref, () => actions);

    const [getUsers, { loading: loadingSearchedUsers, data, refetch: refetchUsers }] =
      useLazyGetUsersForUserSelect();
    const [
      getUsersByResourceCode,
      { loading: loadingUserByResourceCode, data: userByResourceCode },
    ] = useLazyUsersByResourceCode();

    const filterByResourceCode = React.useMemo(() => {
      return !!blockGuidList && blockGuidList.length > 0;
    }, [blockGuidList]);

    React.useEffect(() => {
      if (filterByResourceCode && blockGuidList) {
        getUsersByResourceCode({
          variables: {
            getUsersByResourceCodeQ: {
              resourceCode: blockGuidList.join(','),
              containOneself: true,
            },
          },
        });
      } else {
        getUsers({
          variables: {
            authorized: true,
            resourceParams: idc ? [{ resourceCodes: [idc], resourceType: 'IDC' }] : undefined,
          },
        });
      }
    }, [blockGuidList, filterByResourceCode, getUsers, getUsersByResourceCode, idc, visible]);

    React.useEffect(() => {
      if (!filterByResourceCode && searchValue) {
        refetchUsers({
          key: searchValue,
        });
      }
    }, [filterByResourceCode, refetchUsers, searchValue]);

    const options = React.useMemo(() => {
      if (filterByResourceCode) {
        return (userByResourceCode?.getUsersByResourceCode ?? [])
          .map(user => ({
            id: user.id,
            name: user.name,
            login: user.loginName,
          }))
          .filter(user =>
            searchValue
              ? user.login?.includes(searchValue) || user.name.includes(searchValue)
              : true
          ) as { id: number; name: string; login: string; email?: string }[];
      }
      return (data?.usersForUserSelect ?? [])
        .map(user => ({
          id: user.id,
          name: user.name,
          login: user.login,
          email: user.email,
        }))
        .filter(_user =>
          !includeCurrentUser ? user.userId!.toString() !== _user!.id.toString() : true
        ) as {
        id: number;
        name: string;
        login: string;
        email?: string;
      }[];
    }, [
      data?.usersForUserSelect,
      filterByResourceCode,
      includeCurrentUser,
      searchValue,
      user.userId,
      userByResourceCode?.getUsersByResourceCode,
    ]);

    return (
      <>
        <Popup
          ref={innerRef}
          visible={openPicker ?? visible}
          title={title}
          bodyStyle={{
            height: '80vh',
            overflowY: 'auto',
          }}
          contentStyle={{
            padding: '10px 0 0',
          }}
          destroyOnClose
          footer={multiple}
          onMaskClick={() => {
            onMaskClick?.();
            setOpenPicker(false);
          }}
          onClickArrow={() => {
            setOpenPicker(false);
          }}
          onCancle={() => {
            setOpenPicker(false);
          }}
          onOK={() => {
            onChange?.(innerValues);
            setOpenPicker(false);
          }}
        >
          <Block style={{ padding: 0 }}>
            <Space style={{ width: '100%', '--gap-vertical': '8px' }} direction="vertical">
              <SearchBar
                style={{ marginBottom: 8, margin: '8px 12px 0' }}
                placeholder={
                  filterByResourceCode ? '请输入用户名查询' : '请根据用户id,用户名或邮箱搜索'
                }
                onChange={value => {
                  setSearchValue(value);
                }}
              />
              {loadingSearchedUsers || loadingUserByResourceCode ? (
                <Loading fullPage />
              ) : options.length === 0 ? (
                <Empty description="暂无数据" />
              ) : (
                <CheckList
                  multiple={multiple}
                  value={
                    multiple
                      ? innerValues.map(user => user.id.toString())
                      : value?.map(user => user.id.toString())
                  }
                  onChange={value => {
                    const labelInValues = value?.map(id =>
                      options?.find(user => user.id.toString() === id)
                    );
                    if (multiple) {
                      setInnerValues(labelInValues.filter(Boolean) as unknown as LabelInValue[]);
                    } else {
                      onChange?.(labelInValues.filter(Boolean) as unknown as LabelInValue[]);
                      setOpenPicker(false);
                    }
                  }}
                >
                  {options.map(user => (
                    <CheckList.Item key={user.id} value={user.id.toString()}>
                      <Space style={{ '--gap-vertical': '0px' }} direction="vertical">
                        <Space>
                          <Typography.Text>{user.name}</Typography.Text>
                          <Typography.Text>{user.login}</Typography.Text>
                        </Space>
                        {showUserEmail && user?.email && (
                          <Typography.Text type="secondary">{user.email}</Typography.Text>
                        )}
                      </Space>
                    </CheckList.Item>
                  ))}
                </CheckList>
              )}
            </Space>
          </Block>
        </Popup>
        {showLabel && (
          <Space
            style={{ width: '85vw' }}
            onClick={() => {
              setOpenPicker(true);
              setInnerValues(value ?? []);
            }}
          >
            {value && value.length > 0 ? (
              <Typography.Text style={{ maxWidth: '90vw' }} ellipsis>
                {value
                  .map(user => (showUserEmail ? `${user.name}(${user.email})` : `${user.name}`))
                  .join('｜')}
              </Typography.Text>
            ) : (
              <span style={{ color: 'var(--adm-color-light)' }}>
                {intl.getMessage('common.placeholder', '请选择')}
              </span>
            )}
          </Space>
        )}
      </>
    );
  }
);

UsersCheckListPicker.displayName = 'UserEmailsPicker';
