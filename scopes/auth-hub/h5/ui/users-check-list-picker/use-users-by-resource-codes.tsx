import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

export const GET_USERS_BY_RESOURCE_CODE = gql`
  query GetUsersByResourceCode($getUsersByResourceCodeQ: GetUsersByResourceCodeQ) {
    getUsersByResourceCode(getUsersByResourceCodeQ: $getUsersByResourceCodeQ) {
      id
      name
      loginName
    }
  }
`;

export type GetUsersByResourceCodeResponse = {
  getUsersByResourceCode?: {
    id: number;
    name: String;
    loginName: String;
  }[];
};

export type QueryGetUsersByResourceCodeArgs = {
  getUsersByResourceCodeQ: {
    containOneself?: boolean;
    resourceCode?: string;
  };
};

export const useUsersByResourceCode = (
  options?: QueryHookOptions<GetUsersByResourceCodeResponse, QueryGetUsersByResourceCodeArgs>
): QueryResult<GetUsersByResourceCodeResponse, QueryGetUsersByResourceCodeArgs> =>
  useQuery(GET_USERS_BY_RESOURCE_CODE, {
    fetchPolicy: 'network-only',
    ...options,
  });

export function useLazyUsersByResourceCode(
  options?: LazyQueryHookOptions<GetUsersByResourceCodeResponse, QueryGetUsersByResourceCodeArgs>
): LazyQueryResultTuple<GetUsersByResourceCodeResponse, QueryGetUsersByResourceCodeArgs> {
  return useLazyQuery(GET_USERS_BY_RESOURCE_CODE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
