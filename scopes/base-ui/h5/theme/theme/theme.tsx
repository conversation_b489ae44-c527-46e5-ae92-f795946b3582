import React from 'react';

import './styles/light-theme.less';
import './theme.less';

export type ThemeContextType = {};

export type ThemeType = 'red' | 'green';
export type ThemeMode = 'dark' | 'light';

export type ThemeProviderProps = {
  theme?: ThemeType;
  mode?: ThemeMode;
  children: React.ReactNode;
};

export const ThemeContext = React.createContext({
  theme: 'green',
  mode: 'dark',
});

export const useThemeContext = () => React.useContext(ThemeContext);

export function ThemeProvider({ theme = 'green', mode = 'light', children }: ThemeProviderProps) {
  React.useEffect(() => {
    document.body.className = `${theme}-theme`;
  }, [theme]);

  return <ThemeContext.Provider value={{ theme, mode }}>{children}</ThemeContext.Provider>;
}
