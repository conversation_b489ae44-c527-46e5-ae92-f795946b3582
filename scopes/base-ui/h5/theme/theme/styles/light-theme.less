body.red-theme {
  height: 100%;

  --adm-font-size-main: 14px; // 文字大小

  // Color
  // ----------------
  --adm-color-primary: #b81c22; // 主题色
  --adm-color-success: #00b578; // 表示成功的颜色
  --adm-color-warning: #faad14; // 表示警告的颜色
  --adm-color-danger: #f5222d; // 表示错误的颜色
  --adm-color-warning-hover: #fffbe8;

  --adm-color-blue: #1890ff; // 蓝色
  --adm-color-white: white; // 白色
  --adm-color-weak: fade(black, 45%); // 文字副标题的颜色
  --adm-color-light: rgba(134, 144, 156, 1); //一些符号会用到的颜色
  --adm-icon-color: fade(black, 25%); // 图标颜色
  --adm-border-color: #eeeeee; // 边框颜色
  --adm-border-light-color: rgba(0, 0, 0, 0.06); // 边框颜色
  --adm-border-lighter-color: #e9eff6; // 边框颜色
  --adm-color-text: rgba(78, 89, 105, 1); // （次强调标题）文字颜色
  --adm-color-title: rgba(29, 33, 41, 1); // （强调标题）文字颜色
  --form-list-item-margin-bottom: 10px; /*表单项间距***/

  // @deprecated
  --head-bar-text: white;
  --viewer-bg: white;
  --backgroud-color: rgba(184, 28, 34, 0.08);
  --backgroud-color2: #e8fff4;
  --backgroud-color3: #f2fcf8;
  // @deprecated
  --root-bg: #f0f2f5;

  --adm-component-bg: white;

  --scrollbar-bg: fade(#000, 15%); //滚动条背景色

  --scrollbar-hover-bg: fade(#000, 45%); //滚动条hover颜色

  --adm-border-light-color: #e4e7eb; //边框颜色(亮)

  --adm-color-gray-1: #f7f8fa; //表示灰色1
  --adm-color-gray-2: #f2f3f5; //表示灰色2
  --background-color4: #f8fafc;

  --divider-background: #f2f6fb;
}

body.green-theme {
  height: 100%;

  --adm-font-size-main: 14px; // 文字大小

  // Color
  // ----------------
  --adm-color-primary: #00b578; // 主题色
  --adm-color-success: #00b578; // 表示成功的颜色
  --adm-color-warning: #faad14; // 表示警告的颜色
  --adm-color-danger: #f5222d; // 表示错误的颜色
  --adm-color-warning-hover: #fffbe8;

  --adm-color-blue: #1890ff; // 蓝色
  --adm-color-white: white; // 白色
  --adm-color-weak: fade(black, 45%); // 文字副标题的颜色
  --adm-color-light: rgba(134, 144, 156, 1); //一些符号会用到的颜色
  --adm-icon-color: fade(black, 25%); // 图标颜色
  --adm-border-color: #eeeeee; // 边框颜色
  --adm-border-light-color: rgba(0, 0, 0, 0.06); // 边框颜色
  --adm-border-lighter-color: #e9eff6; // 边框颜色
  --adm-color-text: rgba(78, 89, 105, 1); // （标题）文字颜色
  --adm-color-title: rgba(29, 33, 41, 1); // （标题）文字颜色
  --form-list-item-margin-bottom: 10px; /*表单项间距***/

  // @deprecated
  --head-bar-text: white;
  --viewer-bg: white;
  --backgroud-color: rgba(0, 181, 120, 0.1);
  --backgroud-color2: #e8fff4;
  --backgroud-color3: #f2fcf8;
  // @deprecated
  --root-bg: #f0f2f5;

  --adm-component-bg: white;

  --scrollbar-bg: fade(#000, 15%); //滚动条背景色

  --scrollbar-hover-bg: fade(#000, 45%); //滚动条hover颜色

  --adm-border-light-color: #e4e7eb; //边框颜色(亮)

  --adm-color-gray-1: #f7f8fa; //表示灰色1
  --adm-color-gray-2: #f2f3f5; //表示灰色2
  --background-color4: #f8fafc;
  --divider-background: #f2f6fb;
}
