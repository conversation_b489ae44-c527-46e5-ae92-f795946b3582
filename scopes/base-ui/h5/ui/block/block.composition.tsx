import React from 'react';

import { Block } from './block';

export const BasicBlock = () => {
  return (
    <div style={{ width: '100%', height: '60%', background: '#ccc', overflow: 'height' }}>
      <Block style={{ height: '100px', border: '1px solid gray' }} top={false}>
        <div style={{ height: '40px' }}>Block内容</div>
      </Block>
      <Block style={{ height: '100px', border: '1px solid gray' }} top={20} bottom={false}>
        <div style={{ height: '40px' }}>Block2内容</div>
      </Block>
      <Block style={{ height: '100px', border: '1px solid gray' }} top={false}>
        <div style={{ height: '40px' }}>Block3内容</div>
      </Block>
    </div>
  );
};

export const DiffSizeBasicBlock = () => {
  return (
    <div style={{ width: '100%', height: '60%', background: '#ccc', overflow: 'height' }}>
      <Block style={{ height: '100px' }} size="large" border>
        <div style={{ height: '40px' }}>size: large </div>
      </Block>
      <Block style={{ height: '100px' }} size="middle">
        <div style={{ height: '40px' }}>size: middle</div>
      </Block>
      <Block style={{ height: '100px' }} size="small">
        <div style={{ height: '40px' }}>size: small</div>
      </Block>
      <Block style={{ height: '100px' }} size="x-small">
        <div style={{ height: '40px' }}>size: x-small</div>
      </Block>
      <Block style={{ height: '100px' }} size="xs-small">
        <div style={{ height: '40px' }}>size: xs-small</div>
      </Block>
    </div>
  );
};
