import classNames from 'classnames';
import React, { type ReactNode } from 'react';

import { Loading } from '@manyun/base-ui.h5.ui.loading';

import bg from './asset/bg.png';
import './block.less';

const prefixClass = 'adm-block';

export type BlockProps = {
  style?: React.CSSProperties;
  id?: string;
  top?: number | boolean;
  bottom?: number | boolean;
  className?: string;
  children?: ReactNode;
  size?: SizeType;
  color?: ColorType;
  border?: boolean;
  radius?: number | SizeType;
  actions?: React.ReactNode[];
  loading?: boolean;
  onClick?: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  /**
   * 是否显示默认背景图片
   */
  showBg?: boolean;
  showBoxShadow?: boolean;
  showDefaultBgColor?: boolean;
};

export type SizeType = 'small' | 'middle' | 'large' | 'x-small' | 'xs-small';
export type ColorType = 'primary' | 'success' | 'error' | 'warning' | 'gray-1' | 'gray-2';

function getAction(actions: React.ReactNode[]): React.ReactNode[] {
  return actions.map<React.ReactNode>((action, index) => (
    // eslint-disable-next-line react/no-array-index-key
    <li key={`action-${index}`} style={{ width: `${100 / actions.length}%` }}>
      <span>{action}</span>
    </li>
  ));
}

export const Block = React.forwardRef<HTMLDivElement, BlockProps>(
  (
    {
      style,
      id,
      className,
      loading,
      top = false,
      bottom = false,
      children,
      size,
      color,
      border,
      radius,
      showBg = false,
      showDefaultBgColor = false,
      showBoxShadow = false,
      actions,
      onClick,
    },
    ref
  ) => {
    const radiusSize = radius ? (typeof radius === 'number' ? radius : undefined) : undefined;
    const actionDom =
      actions && actions.length ? (
        <ul
          style={{
            borderBottomLeftRadius: radiusSize,
            borderBottomRightRadius: radiusSize,
          }}
          className={classNames(`${prefixClass}-actions`, color && `${prefixClass}-${color}`)}
        >
          {getAction(actions)}
        </ul>
      ) : null;

    const blockStyle: React.CSSProperties = {
      marginTop: typeof top == 'boolean' ? (top === false ? 0 : 10) : top,
      marginBottom: typeof bottom == 'boolean' ? (bottom === false ? 0 : 10) : bottom,
      border: border ? '1px solid  var(--adm-border-light-color)' : undefined,
      borderRadius: radiusSize,
      boxShadow: showBoxShadow
        ? '-20px 40px 100px 0px rgba(88, 92, 98, 0.04), -4px 8px 20px 0px rgba(39, 217, 228, 0.08)'
        : undefined,
    };

    if ((actions ?? []).length > 0) {
      blockStyle.borderBottomLeftRadius = 0;
      blockStyle.borderBottomRightRadius = 0;
    }

    const bgStyle: React.CSSProperties = showBg
      ? {
          backgroundImage: `url(${bg})`,
          backgroundSize: 'cover',
          width: '100%',
          height: 'auto',
          border: '1px solid var(--adm-color-white)',
        }
      : {};

    return (
      <React.Fragment>
        <div
          ref={ref}
          id={id}
          className={classNames(
            className,
            prefixClass,
            size && `${prefixClass}-${size}`,
            color && `${prefixClass}-${color}`,
            radius && typeof radius === 'string' && `${prefixClass}-radius-${radius}`
          )}
          style={{
            position: showDefaultBgColor ? 'relative' : undefined,
            ...blockStyle,
            ...bgStyle,
            ...style,
          }}
          onClick={onClick}
        >
          {showDefaultBgColor && (
            <div
              style={{
                position: 'absolute',
                top: 2,
                left: 2,
                right: 2,
                width: '99%',
                height: '100%',
                maxHeight: '800px',
                zIndex: 2,
                background: 'linear-gradient(4.29deg, #FFFFFF 88.51%, #F3FFFB 100%)',
                borderRadius: radiusSize,
              }}
            />
          )}
          {loading ? (
            <Loading
              style={{
                zIndex: 999,
                color: 'var(--adm-color-primary)',
                position: showDefaultBgColor ? 'relative' : undefined,
                height: '100%',
              }}
              fullPage
            />
          ) : showDefaultBgColor ? (
            <div
              style={{
                zIndex: 999,
                position: showDefaultBgColor ? 'relative' : undefined,
              }}
            >
              {children}
            </div>
          ) : (
            children
          )}
        </div>
        {actionDom}
      </React.Fragment>
    );
  }
);
Block.displayName = 'Block';
