@block-prefix-cls: ~'adm-block';
@block-base-margin: 10px 0;
@block-base-background: var(--adm-color-white);
@padding-lg: 24px;
@padding-md: 16px;
@padding-sm: 12px;
@padding-xs: 8px;
@padding-xss: 4px;

.@{block-prefix-cls}, .@{block-prefix-cls}-actions {
  padding: @padding-sm;
  background-color: @block-base-background;
  box-sizing: border-box;
  width: 100%;
  &.@{block-prefix-cls}-large {
    padding: @padding-lg;
  }

  &.@{block-prefix-cls}-middle {
    padding: @padding-md;
  }

  &.@{block-prefix-cls}-small {
    padding: @padding-sm;
  }
  &.@{block-prefix-cls}-x-small {
    padding: @padding-xs;
  }
  &.@{block-prefix-cls}-xs-small {
    padding: @padding-xss;
  }

  &.@{block-prefix-cls}-primary {
    background-color: var(--adm-color-primary);
  }

  &.@{block-prefix-cls}-success {
    background-color: var(--adm-color-success);
  }

  &.@{block-prefix-cls}-error {
    background-color: var(--adm-color-error);
  }

  &.@{block-prefix-cls}-warning {
    background-color: var(--adm-color-warning);
  }

  &.@{block-prefix-cls}-gray-1{
    background-color: var(--adm-color-gray-1);
  }
  &.@{block-prefix-cls}-gray-2{
    background-color:var(--adm-color-gray-2);
  }


  &.@{block-prefix-cls}-radius-large {
    border-radius: @padding-lg;
  }

  &.@{block-prefix-cls}-radius-middle {
    border-radius: @padding-md;
  }

  &.@{block-prefix-cls}-radius-small {
    border-radius: @padding-sm;
  }
  &.@{block-prefix-cls}-radius-x-small {
    border-radius: @padding-xs;
  }
  &.@{block-prefix-cls}-radius-xs-small {
    border-radius: @padding-xss;
  }
  
}

.@{block-prefix-cls}-actions {
  display: flex;
  margin: 0;
  padding: 0;
  border-top: 1px solid var(--adm-color-border) ;

  & > li {
    padding: 2px  0;
    text-align: center;

    > span {
      position: relative;
      display: block;
      min-width: 32px;
    }

    &:not(:last-child) {
      border-right: 1px solid var(--adm-color-border);
    }
  }
}