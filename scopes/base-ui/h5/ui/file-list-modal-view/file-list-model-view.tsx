import React, { useState } from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { FilePreview } from '@manyun/base-ui.h5.ui.file-preview';
import type { PreviewFile } from '@manyun/base-ui.h5.ui.file-preview';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { Typography } from '@manyun/base-ui.h5.ui.typography';

import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

export type FileListModelViewProps = {
  fileList: BackendMcUploadFile[];
};
export const FileListModelView = ({ fileList }: FileListModelViewProps) => {
  const [open, setOpen] = useState(false);
  if (fileList.length === 0) {
    return '--';
  }
  return (
    <>
      <Typography.Text
        type="success"
        onClick={() => {
          setOpen(true);
        }}
      >
        查看
      </Typography.Text>
      <Popup
        bodyStyle={{ height: '50vh' }}
        title="附件"
        visible={open}
        contentStyle={{ padding: '10px 0 0' }}
        showCloseButton
        onClose={() => {
          setOpen(false);
        }}
        onMaskClick={() => {
          setOpen(false);
        }}
      >
        <Block style={{ width: '100%', height: '100%', overflowY: 'auto' }}>
          <FilePreview
            files={fileList.map(file => McUploadFile.fromApiObject(file)) as PreviewFile[]}
            showPreview
          />
        </Block>
      </Popup>
    </>
  );
};
