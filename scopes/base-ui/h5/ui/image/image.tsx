import React from 'react';

import { Image as BasicImage, ImageViewer } from 'antd-mobile';
import type { ImageProps as BaiscImageProps } from 'antd-mobile/es/components/image';

type ImageProps = {
  fullScreen?: boolean;
} & BaiscImageProps;

const Image = ({ fullScreen, ...props }: ImageProps) => {
  const [visible, setVisible] = React.useState(false);
  return (
    <>
      <BasicImage
        {...props}
        onClick={e => {
          if (fullScreen) {
            setVisible(true);
          }
          props.onClick?.(e);
        }}
      />
      <ImageViewer
        getContainer={() => document.body}
        image={props.src}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
      />
    </>
  );
};
export { Image };
export type { ImageProps };
