import React, { useState } from 'react';

import { Button } from 'antd-mobile';

import { ActionSheet } from './action-sheet';
import type { Action } from './action-sheet';

const actions: Action[] = [
  { text: '复制', key: 'copy' },
  { text: '修改', key: 'edit' },
  { text: '删除', key: 'delete' },
];
export const BasicActionSheet = () => {
  const [visible, setVisible] = useState(false);
  return (
    <>
      <Button onClick={() => setVisible(true)}>最简单的用法</Button>
      <ActionSheet visible={visible} actions={actions} onClose={() => setVisible(false)} />
    </>
  );
};

export const WithCancelButtonAndDescription = () => {
  const [visible, setVisible] = useState(false);
  return (
    <>
      <Button onClick={() => setVisible(true)}>取消按钮和额外描述</Button>
      <ActionSheet
        extra="请选择你要进行的操作"
        cancelText="取消"
        visible={visible}
        actions={actions}
        onClose={() => setVisible(false)}
      />
    </>
  );
};
