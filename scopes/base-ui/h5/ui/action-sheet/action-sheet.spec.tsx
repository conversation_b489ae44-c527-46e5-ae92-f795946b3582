import React from 'react';

import { fireEvent, render } from '@testing-library/react';

import { BasicActionSheet, WithCancelButtonAndDescription } from './action-sheet.composition';

it('should render with the correct text', async () => {
  const { getByText } = render(<BasicActionSheet />);
  const rendered = getByText('最简单的用法');
  expect(rendered).toBeTruthy();
  fireEvent.click(rendered);
  await new Promise(resolve => setTimeout(resolve, 1000));
  expect(getByText('复制')).toBeTruthy();
});

it('should render with cancle and extranl description', async () => {
  const { getByText } = render(<WithCancelButtonAndDescription />);
  const rendered = getByText('取消按钮和额外描述');
  expect(rendered).toBeTruthy();
  fireEvent.click(rendered);
  await new Promise(resolve => setTimeout(resolve, 1000));
  expect(getByText('复制')).toBeTruthy();
});
