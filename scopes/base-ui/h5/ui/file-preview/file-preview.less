@class-prefix-file-preview: ~'adm-file-preview';

.@{class-prefix-file-preview} {
  width: 100%;
  max-width: calc(100vw - 27px);
  .filePreviewItem {
    position: relative;
    width: 100%;
    display: flex;
    gap: 6px;
  }
  .filePreviewItem-fail {
    border: 1px solid var(--adm-color-danger);
    box-sizing: border-box;
    border-radius: 4px;
    padding: 5px 0;
  }
  &-fail {
    color: var(--adm-color-danger) !important;
  }
  /**Loading**/
  &-mask {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    color: var(--adm-color-white);
    background-color: rgba(84, 83, 83, 0.68);
  }

  &-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    box-sizing: border-box;
    padding-top: 8px;
  }

  &-mask-message {
    display: inline-block;
    padding: 6px 4px;
    font-size: 12px;
    color: var(--adm-color-primary);
  }
}

// 预览样式
