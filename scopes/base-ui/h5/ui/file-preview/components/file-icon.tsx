import React from 'react';

import {
  ImageColorful,
  PdfColorful,
  UnknownFileColorful,
  VideoColorful,
} from '@manyun/base-ui.icons';
import { MimeType, guessMimeType } from '@manyun/base-ui.util.guess-mime-type';
import type { File } from '@manyun/base-ui.util.guess-mime-type';

import styles from './styles.module.less';

export type FileType = 'image' | 'video' | 'pdf' | 'others';

export function FileIcon({ file }: { file: File }) {
  const fileType = getFileType(file);

  switch (fileType) {
    case 'image':
      return <ImageColorful className={styles.fileIcon} />;
    case 'video':
      return <VideoColorful className={styles.fileIcon} />;
    case 'pdf':
      return <PdfColorful className={styles.fileIcon} />;
    default:
      return <UnknownFileColorful className={styles.fileIcon} />;
  }
}

export function getFileType(file: File): FileType {
  const type = guessMimeType(file);
  switch (type) {
    case MimeType.Image:
      return 'image';
    case MimeType.Video:
      return 'video';
    case MimeType.Pdf:
      return 'pdf';
    default:
      return 'others';
  }
}
