import CloseCircleFilled from '@ant-design/icons/CloseCircleFilled';
import FileFilled from '@ant-design/icons/FileFilled';
import classNames from 'classnames';
import React, { useState } from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { Button } from '@manyun/base-ui.h5.ui.button';
import { Divider } from '@manyun/base-ui.h5.ui.divider';
import { ImageViewer } from '@manyun/base-ui.h5.ui.image-viewer';
import { Loading } from '@manyun/base-ui.h5.ui.loading';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { SafeArea } from '@manyun/base-ui.h5.ui.safe-area';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Typography } from '@manyun/base-ui.h5.ui.typography';

import { FileIcon } from './components/file-icon';
import './file-preview.less';

const classPrefix = `adm-file-preview`;

export type PreviewFileStatus = 'pending' | 'fail';

export type PreviewFile = {
  id: number | string;
  key?: number;
  ext: string /**文件格式 */;
  thumbnailUrl?: string;
  src: string /**文件路径 */;
  name: string /**文件名称 */;
  size: number /**文件大小 */;
  status?: PreviewFileStatus;
};

export type FilePreviewProps = {
  files: PreviewFile[];
  iconSize?: number;
  style?: React.CSSProperties;
  className?: string;
  showPreview?: boolean;
  showPreviewBtn?: boolean;
  showDivider?: boolean;
  onDelete?: (file?: PreviewFile) => void;
};
export function FilePreview({
  files,
  style,
  className,
  iconSize = 50,
  showPreview = false,
  showPreviewBtn = true,
  showDivider = false,
  onDelete,
}: FilePreviewProps) {
  if (files.length === 0) {
    return <></>;
  }

  return (
    <Space
      direction="vertical"
      className={classNames(classPrefix, className)}
      style={{ '--gap': '10px', ...style }}
      justify="center"
    >
      {files.map((file, index) => (
        <div key={file.id}>
          <PreviewItem
            key={`${file.id}_${file.thumbnailUrl}`}
            file={file}
            iconSize={iconSize}
            showPreview={showPreview}
            showPreviewBtn={showPreviewBtn}
            deletable={onDelete ? true : false}
            onDelete={onDelete}
          />
          {showDivider && <Divider style={{ margin: '5px 0 0' }} />}
        </div>
      ))}
    </Space>
  );
}

const isImage = (ext: string) => {
  var patt = /^image\/*/;
  return ['.jpeg', '.png', '.gif', '.jpg', '.webp'].includes(ext.toLowerCase()) || patt.test(ext);
};

export type PreviewItemProps = {
  index?: number;
  url?: string;
  file: PreviewFile;
  iconSize?: number;
  showPreview?: boolean;
  showPreviewBtn?: boolean;
  deletable: boolean;
  deleteIconStyle?: React.CSSProperties;
  onClick?: () => void;
  onDelete?: (file?: PreviewFile) => void;
};
export function PreviewItem({
  file,
  iconSize = 55,
  showPreview = false,
  showPreviewBtn = false,
  deletable = true,
  deleteIconStyle = {},
  onDelete,
}: PreviewItemProps) {
  const [visible, setVisible] = useState(false);

  function renderLoading() {
    return (
      file.status === 'pending' && (
        <div className={`${classPrefix}-mask`}>
          <span className={`${classPrefix}-loading`}>
            <Loading type="spin" color="var(--adm-color-primary)" />
            <span className={`${classPrefix}-mask-message`}>上传中</span>
          </span>
        </div>
      )
    );
  }

  const isImg = isImage(file.ext);

  return (
    <>
      <div
        key={file?.id}
        className={classNames('filePreviewItem', file.status === 'fail' && `filePreviewItem-fail`)}
      >
        <FileIcon file={file} />
        <Space
          direction="vertical"
          style={{
            flex: 1,
            overflowX: 'hidden',
            '--gap': '0',
          }}
          onClick={() => {
            showPreview && setVisible(true);
          }}
        >
          <Typography.Text
            className={classNames(file.status === 'fail' && `${classPrefix}-fail`)}
            style={{ fontSize: '14px' }}
            ellipsis
          >
            {file.name}
          </Typography.Text>
          <Typography.Text
            type="secondary"
            className={classNames(file.status === 'fail' && `${classPrefix}-fail`)}
            style={{ fontSize: '13px' }}
          >
            {(Number(file.size ?? 0) / 1024).toFixed(1)}KB
          </Typography.Text>
        </Space>
        {showPreview && showPreviewBtn && (
          <Space direction="vertical" justify="center">
            <Button
              color="primary"
              fill="outline"
              size="mini"
              onClick={() => {
                setVisible(true);
              }}
            >
              预览
            </Button>
          </Space>
        )}
        {onDelete && deletable && (
          <Space
            style={{ width: '60px', textAlign: 'center', ...deleteIconStyle }}
            direction="vertical"
            justify="center"
            onClick={() => {
              onDelete(file);
            }}
          >
            <CloseCircleFilled
              style={{ fontSize: 22, color: 'rgba(199, 208, 221, 1)', ...deleteIconStyle }}
            />
          </Space>
        )}
        {renderLoading()}
      </div>
      {isImg ? (
        <ImageViewer
          getContainer={() => document.body}
          visible={visible}
          image={file.src}
          onClose={() => {
            setVisible(false);
          }}
        />
      ) : (
        <Popup
          visible={visible}
          title="附件预览"
          arrow
          footer={
            <Space direction="vertical" style={{ padding: '10px', '--gap': '0' }}>
              <Button
                block
                onClick={() => {
                  setVisible(false);
                }}
              >
                取消
              </Button>
              <SafeArea position="bottom" />
            </Space>
          }
          bodyStyle={{ height: 'auto' }}
          contentStyle={{ padding: '6px 0 1px 0' }}
          onMaskClick={() => {
            setVisible(false);
          }}
          onClickArrow={() => {
            setVisible(false);
          }}
          onFooterClick={type => {
            if (type === 'cancle') {
              setVisible(false);
            } else {
              const ele = document.createElement('a');
              ele.download = file.name;
              ele.style.display = 'none';
              ele.href = window.location.origin + file.src;
              document.body.appendChild(ele);
              ele.click();
              document.body.removeChild(ele);
            }
          }}
        >
          <Block style={{ padding: '40px 0 90px ', textAlign: 'center' }}>
            <Space direction="vertical" justify="center" align="center" style={{ width: '100%' }}>
              <FileFilled
                style={{
                  fontSize: iconSize + 'px',
                  color: 'var(--adm-color-primary)',
                  maxWidth: '100vw',
                }}
              />
              <Typography.Title level={5} ellipsis style={{ maxWidth: '90vw' }}>
                {file.name}
              </Typography.Title>
              <Typography.Text type="secondary" ellipsis>
                移动端暂不支持附件下载，请您通过电脑端操作
              </Typography.Text>
            </Space>
          </Block>
        </Popup>
      )}
    </>
  );
}
