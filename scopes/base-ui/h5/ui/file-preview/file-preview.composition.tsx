import React from 'react';

import { destroyMock, webRequest } from '@manyun/service.request';

import { FilePreview } from './file-preview';

export const BasicFilePreview = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
    return () => {
      // mockOff();
    };
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <FilePreview
      files={[
        {
          ext: '.jpeg',
          id: 1648629418572.5022,
          name: '7A42446B-7B41-4291-AF6F-D373954EBD6B.jpeg',
          size: 236805,
          src: 'http://localhost:3400/api/dcom/file/download?filePath=group1/M00/00/01/rBAAD2JEFqqAWWMtAAOdBXxdk4446.jpeg&fileName=7A42446B-7B41-4291-AF6F-D373954EBD6B.jpeg',
        },
        {
          ext: '.pdf',
          id: 1648629418572,
          name: '7A42446B-7B41-4291-AF6F-D373954EBD6B.pdf',
          size: 236805,
          src: 'http://localhost:3400/api/dcom/file/download?filePath=group1/M00/00/01/rBAAD2JEFqqAWWMtAAOdBXxdk4446.jpeg&fileName=7A42446B-7B41-4291-AF6F-D373954EBD6B.jpeg',
        },
      ]}
      showPreview={true}
      showDivider={true}
    />
  );
};

export const BasicFilePreview2 = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
    return () => {
      // mockOff();
    };
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <FilePreview
      files={[
        {
          ext: '.jpeg',
          id: 1648629418572.5022,
          name: '7A42446B-7B41-4291-AF6F-D373954EBD6B.jpeg',
          size: 236805,
          src: 'http://localhost:3400/api/dcom/file/download?filePath=group1/M00/00/01/rBAAD2JEFqqAWWMtAAOdBXxdk4446.jpeg&fileName=7A42446B-7B41-4291-AF6F-D373954EBD6B.jpeg',
        },
        {
          ext: '.pdf',
          id: 1648629418572,
          name: '7A42446B-7B41-4291-AF6F-D373954EBD6B.pdf',
          size: 236805,
          src: 'http://localhost:3400/api/dcom/file/download?filePath=group1/M00/00/01/rBAAD2JEFqqAWWMtAAOdBXxdk4446.jpeg&fileName=7A42446B-7B41-4291-AF6F-D373954EBD6B.jpeg',
        },
      ]}
      onDelete={file => {
        // console.log(file);
      }}
    />
  );
};
