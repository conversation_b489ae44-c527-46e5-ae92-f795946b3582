---
description: A Grid component.
---

import { Grid } from './grid';

### Component usage

请参考 [antd-mobile Grid](https://mobile.ant.design/zh/components/grid) 的文档。

```js
<Grid columns={3} gap={8}>
  <Grid.Item>
    <div className={styles['grid-demo-item-block']}>A</div>
  </Grid.Item>
  <Grid.Item>
    <div className={styles['grid-demo-item-block']}>B</div>
  </Grid.Item>
  <Grid.Item>
    <div className={styles['grid-demo-item-block']}>C</div>
  </Grid.Item>
  <Grid.Item>
    <div className={styles['grid-demo-item-block']}>D</div>
  </Grid.Item>
  <Grid.Item>
    <div className={styles['grid-demo-item-block']}>E</div>
  </Grid.Item>
</Grid>
```
