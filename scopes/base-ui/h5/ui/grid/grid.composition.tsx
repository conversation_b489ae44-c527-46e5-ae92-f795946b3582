import React from 'react';

import { Grid } from './grid';
import styles from './grid.module.less';

export const BasicGrid = () => {
  return (
    <div style={{ width: '300px' }}>
      <Grid columns={3} gap={8}>
        <Grid.Item>
          <div className={styles['grid-demo-item-block']}>A</div>
        </Grid.Item>
        <Grid.Item>
          <div className={styles['grid-demo-item-block']}>B</div>
        </Grid.Item>
        <Grid.Item>
          <div className={styles['grid-demo-item-block']}>C</div>
        </Grid.Item>
        <Grid.Item>
          <div className={styles['grid-demo-item-block']}>D</div>
        </Grid.Item>
        <Grid.Item>
          <div className={styles['grid-demo-item-block']}>E</div>
        </Grid.Item>
      </Grid>
    </div>
  );
};
