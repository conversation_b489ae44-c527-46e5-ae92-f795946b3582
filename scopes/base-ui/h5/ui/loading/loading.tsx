import React from 'react';

import { DotLoading, SpinLoading } from 'antd-mobile';
import type { LoadingProps as AdmLoadingProps } from 'antd-mobile/es/components/loading';

export type LoadingProps = {
  type?: 'dot' | 'spin';
  fullPage?: boolean;
} & AdmLoadingProps;

export function Loading({ type = 'dot', fullPage = false, ...props }: LoadingProps) {
  let style: React.CSSProperties = {};
  if (fullPage) {
    style = {
      width: '100%',
      height: '100%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
    };
  }
  return (
    <div style={style}>
      {type === 'dot' ? (
        <DotLoading {...props} style={{ color: 'var(--adm-color-primary)', ...props.style }} />
      ) : (
        <SpinLoading {...props} style={{ '--size': '26px', ...props.style }} />
      )}
    </div>
  );
}
