@class-prefix-alert: ~'adm-breadcrumb';
@breadcrumb-separator-margin: 0 4px;
@breadcrumb-separator-color: var(--adm-color-weak);

.@{class-prefix-alert} {
  color: var(--adm-color-text);
  font-size: var(--adm-font-size-main);
  a {
    color: var(--adm-color-text);
    transition: color 0.3s;
  }
  & > span:last-child {
    color: var(--adm-color-primary);
    a {
      color: var(--adm-color-primary);
    }
  }

  & > span:last-child &-separator {
    display: none;
  }

  &-separator {
    margin: @breadcrumb-separator-margin;
    color: @breadcrumb-separator-color;
  }
}
