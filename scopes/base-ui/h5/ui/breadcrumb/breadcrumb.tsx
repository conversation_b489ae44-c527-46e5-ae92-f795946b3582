import React, { ReactNode } from 'react';

import classNames from 'classnames';
import toArray from 'rc-util/lib/Children/toArray';

import { cloneElement } from '@manyun/base-ui.h5.ui.descriptions';

import './breadcrumb.less';
import BreadcrumbItem from './item';
import BreadcrumbSeparator from './separator';

export const prefixCls = `adm-breadcrumb`;

export type BreadcrumbProps = {
  separator?: React.ReactNode;
  children?: ReactNode;
  style?: React.CSSProperties;
  className?: string;
};

interface BreadcrumbInterface extends React.FC<BreadcrumbProps> {
  Item: typeof BreadcrumbItem;
  Separator: typeof BreadcrumbSeparator;
}

export const Breadcrumb: BreadcrumbInterface = ({
  separator = '/',
  style,
  className,
  children,
  ...restProps
}) => {
  let crumbs;

  if (children) {
    crumbs = toArray(children).map((element: any, index) => {
      if (!element) {
        return element;
      }

      return cloneElement(element, {
        separator,
        key: index,
      });
    });
  }

  const breadcrumbClassName = classNames(prefixCls, className);

  return (
    <div className={breadcrumbClassName} style={style} {...restProps}>
      {crumbs}
    </div>
  );
};

Breadcrumb.Item = BreadcrumbItem;

Breadcrumb.Separator = BreadcrumbSeparator;
