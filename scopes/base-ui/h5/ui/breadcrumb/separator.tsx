import * as React from 'react';

import { prefixCls } from './breadcrumb';
import './breadcrumb.less';

interface BreadcrumbSeparatorInterface extends React.FC {
  __ANT_BREADCRUMB_SEPARATOR: boolean;
}

const BreadcrumbSeparator: BreadcrumbSeparatorInterface = ({ children }) => {
  return <span className={`${prefixCls}-separator`}>{children || '/'}</span>;
};

BreadcrumbSeparator.__ANT_BREADCRUMB_SEPARATOR = true;

export default BreadcrumbSeparator;
