import React from 'react';

import RightOutlined from '@ant-design/icons/RightOutlined';

import { Breadcrumb } from './breadcrumb';

export const BasicBreadcrumb = () => {
  const [strArrays, setStrArrays] = React.useState(['通讯录', '法务部']);

  const onClick = (index: number) => {
    const newArr = strArrays.slice(0, index + 1);
    setStrArrays(newArr);
  };

  return (
    <Breadcrumb separator={<RightOutlined />}>
      {strArrays.map((str, index) => (
        <Breadcrumb.Item
          key={str}
          onClick={() => {
            onClick(index);
          }}
        >
          {str}
        </Breadcrumb.Item>
      ))}
    </Breadcrumb>
  );
};
