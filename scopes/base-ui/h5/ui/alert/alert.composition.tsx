import React from 'react';

import CheckCircleOutlined from '@ant-design/icons/CheckCircleOutlined';

import { Alert } from './alert';

export const BasicAlert = () => {
  return (
    <>
      <div style={{ padding: '10px' }}>
        <Alert
          // style={{ border: 'none' }}
          icon={<CheckCircleOutlined style={{ fontSize: 18, color: 'red' }} />}
          type="success"
          message="原因"
          description={
            '申请原因，这里是一段无关紧要的申请原因，如果你耐心的看完了这段话，你就会发现你浪费了你生命中宝贵的30s。'
          }
          closable
        />
      </div>
      <div style={{ padding: '10px' }}>
        <Alert
          style={{ border: 'none' }}
          type="error"
          description={
            '申请原因，这里是一段无关紧要的申请原因，如果你耐心的看完了这段话，你就会发现你浪费了你生命中宝贵的30s。'
          }
        />
      </div>
      <div style={{ padding: '10px' }}>
        <Alert
          style={{ border: 'none' }}
          type="info"
          description={
            '申请原因，这里是一段无关紧要的申请原因，如果你耐心的看完了这段话，你就会发现你浪费了你生命中宝贵的30s。'
          }
        />
      </div>
      <div style={{ padding: '10px' }}>
        <Alert
          style={{ border: 'none' }}
          type="warning"
          description={
            '申请原因，这里是一段无关紧要的申请原因，如果你耐心的看完了这段话，你就会发现你浪费了你生命中宝贵的30s。'
          }
        />
      </div>
    </>
  );
};
