import CloseOutlined from '@ant-design/icons/CloseOutlined';
import classNames from 'classnames';
import React, { useState } from 'react';

import './alert.less';

const classPrefix = `adm-alert`;

export type AlertProps = {
  /** Type of Alert styles, options:`success`, `info`, `warning`, `error` default value is `info` */
  type?: 'success' | 'info' | 'warning' | 'error' | 'green';
  /** Whether Alert can be closed */
  closable?: boolean;
  closableStyle?: React.CSSProperties;
  /** Content of Alert */
  message?: React.ReactNode;
  /** Additional content of Alert */
  description?: React.ReactNode;
  /** Callback when close Alert */
  onClose?: () => void;
  style?: React.CSSProperties;
  className?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
};

export function Alert({
  description,
  message,
  className = '',
  style,
  icon,
  closable,
  closableStyle,
  action,
  type = 'info',
  ...props
}: AlertProps) {
  const [visible, setVisible] = useState(true);

  const renderCloseIcon = () =>
    closable ? (
      <div className={`${classPrefix}-close-icon`}>
        <CloseOutlined
          style={{ ...closableStyle }}
          onClick={() => {
            setVisible(false);
            props.onClose?.();
          }}
        />
      </div>
    ) : null;

  if (!visible) {
    return null;
  }

  return (
    <div className={classNames(classPrefix, `${classPrefix}-${type}`, className)} style={style}>
      {icon && <span className={`${classPrefix}-left`}>{icon}</span>}
      <div className={`${classPrefix}-content`}>
        {message ? <div className={`${classPrefix}-message`}>{message}</div> : null}
        {description ? <div className={`${classPrefix}-description`}>{description}</div> : null}
      </div>
      {action ? <div className={`${classPrefix}-action`}>{action}</div> : null}
      {renderCloseIcon()}
    </div>
  );
}
