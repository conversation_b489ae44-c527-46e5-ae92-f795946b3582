@class-prefix-alert: ~'adm-alert';
@border-radius-base: 4px;
@margin-xs: 8px;
@font-size-sm: 14px;

.@{class-prefix-alert} {
  --background-color: var(--adm-color-primary);
  --text-color: var(--adm-color-text);

  --font-size: var(--adm-font-size-main);
  --icon-font-size: 18px;

  box-sizing: border-box;
  padding: 12px 8px;
  position: relative;
  display: flex;
  align-items: center;
  padding: 4px 14px;
  word-wrap: break-word;
  border-radius: @border-radius-base;

  background-color: var(--background-color);

  &.@{class-prefix-alert}-success {
    background-color: var(--adm-color-success);
    border-color: var(--adm-color-success);
  }
  &.@{class-prefix-alert}-warning {
    --background-color: var(--adm-color-warning-hover);
    --border-color: var(--adm-color-warning-hover);
    color: var(--adm-color-warning);
  }

  &.@{class-prefix-alert}-error {
    --background-color: rgba(241, 54, 66, 0.1);
    --border-color: rgba(241, 54, 66, 0.1);
    color: #ff4d4f;
  }
  &.@{class-prefix-alert}-info {
    --background-color: var(--adm-color-primary);
    --border-color: var(--adm-color-primary);
  }

  &.@{class-prefix-alert}-green {
    --background-color: var(--backgroud-color, rgba(0, 181, 120, 0.1));
    --border-color: var(--backgroud-color, rgba(0, 181, 120, 0.1));
  }

  & .@{class-prefix-alert}-left {
    flex-shrink: 0;
    margin-right: 8px;
    font-size: var(--icon-font-size);
    line-height: var(--height);
  }
  & .@{class-prefix-alert}-content {
    flex: 1;
    min-width: 0;
    line-height: 30px;
  }

  &-message {
    font-size: calc(var(--adm-font-size-main) + 1px);
    margin-bottom: 4px;
  }

  &-description {
    font-size: var(--adm-font-size-main);
    color: var(--adm-color-main);
    line-height: calc(var(--adm-font-size-main) + 6px);
    display: block;
  }

  &-close-icon {
    margin-left: @margin-xs;
    margin-top: 6px;
    padding: 0;
    overflow: hidden;
    font-size: @font-size-sm;
    line-height: @font-size-sm;
    background-color: transparent;
    border: none;
    outline: none;
    cursor: pointer;
  }

  &-action {
    margin-left: @margin-xs;
  }
}
