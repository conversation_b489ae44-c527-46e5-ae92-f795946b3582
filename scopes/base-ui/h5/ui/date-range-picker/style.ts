import styled from 'styled-components';

export const StyledContainer = styled.div`
  .timeContainer {
    width: 100%;
    padding: 8px 16px;
    box-sizing: border-box;
    border-bottom: 1px solid var(--adm-border-color);
    .adm-space-item {
      &:first-child {
        flex: 1;
        text-align: center;
        font-weight: 500;
      }
      &:last-child {
        flex: 1;
        text-align: center;
        font-weight: 500;
      }
    }

    .activeTimeSelect {
      position: relative;
      &::after {
        position: absolute;
        content: '';
        width: 24px;
        height: 3px;
        border-radius: 2px;
        bottom: -14px;
        left: 10%;
        background-color: var(--adm-color-primary);
      }
    }
  }
`;
