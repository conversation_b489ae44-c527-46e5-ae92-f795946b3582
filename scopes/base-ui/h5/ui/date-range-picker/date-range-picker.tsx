import classnames from 'classnames';
import dayjs from 'dayjs';
import React from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { DatePickerView } from '@manyun/base-ui.h5.ui.date-picker-view';
import type { DatePickerViewProps } from '@manyun/base-ui.h5.ui.date-picker-view';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Toast } from '@manyun/base-ui.h5.ui.toast';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { formatDateToPrecision } from '@manyun/dc-brain.h5.util.common';

import { StyledContainer } from './style.js';

export type DateRangePickerProps = {
  title?: string;
  open?: boolean;
  defaultValue?: [Date, Date];
  value?: [Date, Date];
  precisionText?: string;
  renderExtra?: React.ReactNode;
  onChange?: (value: [Date, Date]) => void;
  onClose?: () => void;
} & Pick<DatePickerViewProps, 'precision'>;

export function DateRangePicker({
  open,
  defaultValue,
  value,
  precision,
  precisionText,
  onChange,
  onClose,
  renderExtra,
  ...props
}: DateRangePickerProps) {
  const [timeRange, setTimeRange] = React.useState<[Date | undefined, Date | undefined]>(
    defaultValue ?? [undefined, undefined]
  );
  const [activeTime, setActiveTime] = React.useState<'start' | 'end'>('start');
  const [pickerValue, setPickerValue] = React.useState<Date>(defaultValue?.[0] ?? new Date());

  React.useEffect(() => {
    if (value) {
      setTimeRange(value);
      setPickerValue(value[0]);
    }
  }, [value]);

  return (
    <Popup
      bodyStyle={{
        maxHeight: '80vh',
        height: 'auto',
        overflowY: 'auto',
      }}
      autoClose={false}
      contentStyle={{ padding: '8px 0 0' }}
      {...props}
      visible={open}
      footer
      destroyOnClose
      onOK={() => {
        if (!timeRange || !timeRange[0] || !timeRange[1]) {
          Toast.show('请设置时间');
          return;
        }
        if (dayjs(timeRange[0]).isAfter(dayjs(timeRange[1]))) {
          Toast.show('请合理设置区间!');
          return;
        }
        onChange?.(timeRange as [Date, Date]);
        onClose?.();
      }}
      onMaskClick={() => {
        onClose?.();
      }}
      onCancle={() => {
        onClose?.();
      }}
    >
      <StyledContainer>
        <Block style={{ width: '100%', padding: 0 }}>
          <Space className="timeContainer" size={16} align="center">
            <Typography.Text
              style={{ width: '100%', textAlign: 'center' }}
              className={classnames(activeTime === 'start' && 'activeTimeSelect')}
              type={activeTime === 'start' ? 'primary' : undefined}
              onClick={() => {
                setActiveTime('start');
                if (timeRange[0]) {
                  setPickerValue(timeRange[0]);
                }
              }}
            >
              {timeRange[0]
                ? `${formatDateToPrecision(timeRange[0], precision ?? 'month')}${precisionText}`
                : '请选择开始时间'}
            </Typography.Text>
            <Typography.Text>~</Typography.Text>
            <Typography.Text
              style={{ width: '100%', textAlign: 'center' }}
              className={classnames(activeTime === 'end' && 'activeTimeSelect')}
              type={activeTime === 'end' ? 'primary' : undefined}
              onClick={() => {
                setActiveTime('end');
                if (timeRange[1]) {
                  setPickerValue(timeRange[1]);
                }
              }}
            >
              {timeRange[1]
                ? `${formatDateToPrecision(timeRange[1], precision ?? 'month')}${precisionText}`
                : '请选择结束时间'}
            </Typography.Text>
          </Space>
          <DatePickerView
            style={{ marginTop: 24 }}
            value={pickerValue}
            precision={precision}
            min={dayjs().subtract(12, 'year').toDate()}
            max={new Date()}
            onChange={value => {
              setPickerValue(value);
              if (activeTime === 'start') {
                setTimeRange(pre => [value, pre[1]]);
              } else {
                setTimeRange(pre => [pre[0], value]);
              }
            }}
          />
          {renderExtra}
        </Block>
      </StyledContainer>
    </Popup>
  );
}
