import type { ReactNode } from 'react';
import React from 'react';

import { Card as BasicCard } from 'antd-mobile';
import type { CardProps as BasicCardProps } from 'antd-mobile/es/components/card';

import { Divider } from '@manyun/base-ui.h5.ui.divider';

import './card.less';

export type CardProps = BasicCardProps & {
  children?: ReactNode;
  style?: React.CSSProperties;
  className?: string;
  actions?: Array<ReactNode>;
};

export const Card = ({ children, actions = [], ...props }: CardProps) => {
  const gridCount = actions.length;

  return (
    <BasicCard {...props}>
      {children}
      {gridCount > 0 && (
        <>
          <Divider style={{ margin: '12px 0 0' }} />
          <div style={{ display: 'flex' }}>
            {actions.map((action, index) => {
              return (
                // eslint-disable-next-line react/no-array-index-key
                <div key={'grid' + index} style={{ width: '100%', display: 'flex' }}>
                  <div style={{ flex: 'auto' }}>{action}</div>
                  {index !== gridCount - 1 && (
                    // <Divider />
                    <div style={{ backgroundColor: 'var(--adm-border-color)', width: 1 }} />
                  )}
                </div>
              );
            })}
          </div>
        </>
      )}
    </BasicCard>
  );
};
