import React from 'react';

import { Button, Input, Selector } from 'antd-mobile';

import { Form } from './form';

export const BasicForm = () => {
  const onFinish = () => {};
  return (
    <div style={{ background: '#fafafa', width: '100%', height: '100%' }}>
      <Form
        onFinish={onFinish}
        footer={
          <Button block type="submit" color="primary" size="large">
            提交
          </Button>
        }
      >
        <Form.Header>基础用法</Form.Header>
        <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
          <Input placeholder="请输入姓名" />
        </Form.Item>
        <Form.Item name="address" label="地址" help="详情地址">
          <Input placeholder="请输入地址" />
        </Form.Item>
        <Form.Item name="favoriteFruits" label="喜爱的水果">
          <Selector
            columns={3}
            multiple
            options={[
              { label: '苹果', value: 'apple' },
              { label: '橘子', value: 'orange' },
              { label: '香蕉', value: 'banana' },
            ]}
          />
        </Form.Item>
        <Form.Item name="disabledField" label="禁用" disabled>
          <Input placeholder="禁止输入" />
        </Form.Item>
      </Form>
    </div>
  );
};
