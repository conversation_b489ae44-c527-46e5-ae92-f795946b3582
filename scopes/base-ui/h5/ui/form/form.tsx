import React from 'react';

import { Form } from 'antd-mobile';
import type {
  FormItemProps as BasicFormItemProps,
  FieldData,
  FormArrayField,
  FormArrayOperation,
  FormArrayProps,
  FormInstance,
  FormProps,
  FormSubscribeProps,
  NamePath,
  ValidateMessages,
} from 'antd-mobile/es/components/form';
import classNames from 'classnames';

import styles from './form.module.less';

const InternalForm = React.forwardRef<FormInstance, FormProps>((props, ref) => {
  return (
    <Form
      ref={ref}
      {...props}
      style={{
        '--border-bottom': 'none',
        '--border-top': 'none',
        '--border-inner': 'none',
        ...props.style,
      }}
      className={styles.mobileForm}
    />
  );
});
InternalForm.displayName = 'InternalForm';

type FormItemProps = BasicFormItemProps & {
  space?: boolean;
  divider?: boolean;
};

const InternalFormItem = ({ space = false, divider = true, ...props }: FormItemProps) => {
  return (
    <Form.Item
      {...props}
      className={classNames(
        space && styles.mobileSpaceFormItem,
        divider && styles.mobileDividerFormItem,

        props.className
      )}
    />
  );
};

const MCForm = Object.assign(InternalForm, {
  Item: InternalFormItem,
  Subscribe: Form.Subscribe,
  Header: Form.Header,
  Array: Form.Array,
  useForm: Form.useForm,
  useWatch: Form.useWatch,
});

export type {
  FormProps,
  FormInstance,
  FormItemProps,
  FormArrayField,
  FormArrayOperation,
  FormArrayProps,
  FormSubscribeProps,
  ValidateMessages,
  FieldData,
  NamePath,
};
export { MCForm as Form };
