import scrollIntoView from 'scroll-into-view-if-needed';
import type { Options } from 'scroll-into-view-if-needed';

export function scrollToField(fieldId: string | null, options: Options = {}) {
  const node: HTMLElement | null = fieldId ? document.getElementById(fieldId) : null;
  if (node) {
    scrollIntoView(node, {
      scrollMode: 'if-needed',
      block: 'nearest',
      ...options,
    });
  }
}

export function scrollToFirstError(errorFields: any[], options: Options = {}) {
  if (errorFields.length) {
    scrollToField(errorFields[0].name.length ? errorFields[0].name[0] : null, options);
  }
}
