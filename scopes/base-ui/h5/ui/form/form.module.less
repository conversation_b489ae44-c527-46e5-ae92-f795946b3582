@class-prefix: ~'adm';
@class-prefix-form: ~'adm-form';
@form-background-color: var(--root-bg, #f0f2f5);

.mobileForm {
  &:global(.@{class-prefix-form}) {
    padding-top: 16px;
  }
  :global {
    .@{class-prefix-form}-item.@{class-prefix-form}-item-vertical,
    .@{class-prefix}-list-body .@{class-prefix}-list-item-content {
      .@{class-prefix}-form-item-label {
        font-size: 16px;
        line-height: 24px;
        font-weight: 400;
        margin-bottom: 2px;
        color: var(--adm-color-title);
      }
    }
    .@{class-prefix-form}-item.@{class-prefix-form}-item-horizontal.@{class-prefix}-list-item {
      .@{class-prefix}-list-item-content-main {
        text-align: right;
      }
    }
    .@{class-prefix-form}-list-operation {
      line-height: 0;
      color: unset;
    }

    .@{class-prefix}-form-item-child-inner {
      line-height: 24px;
    }
    .@{class-prefix}-input {
      min-height: 24px;
    }
    .@{class-prefix}-list-item-content-arrow {
      font-size: 16px;
      color: rgba(198, 202, 209, 1);
    }
    .@{class-prefix}-list-item {
      padding-right: 16px;
      .@{class-prefix}-list-item-content {
        padding-right: 0;
        .@{class-prefix}-list-item-description {
          font-size: 12px;
        }
      }
    }
  }
  > :global(.@{class-prefix}-list) {
    > :global(.@{class-prefix}-list-header) {
      padding-bottom: 4px;
    }
    > :global(.@{class-prefix}-list-body) {
      background-color: @form-background-color;
      border-bottom-width: 0;
      border-top-width: 0;
      font-size: 16px;
    }
  }

  .mobileSpaceFormItem {
    margin-bottom: 8px;
  }
  .mobileDividerFormItem {
    :global(.@{class-prefix}-list-item-content) {
      border-bottom: 1px solid var(--adm-color-border);
    }
  }
}
