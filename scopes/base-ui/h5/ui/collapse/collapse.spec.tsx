import React from 'react';

import { fireEvent, render, screen } from '@testing-library/react';

import { BasicCollapse } from './collapse.composition';

it('should render with the correct text', async () => {
  render(<BasicCollapse />);
  expect(await screen.queryByTestId('first')).toBeVisible();
  expect(await screen.queryByTestId('second')).toBe(null);
  expect(await screen.queryByTestId('third')).toBe(null);

  // 点击第二项，第二项展开
  fireEvent.click(screen.getByText('第二项'));
  expect(await screen.queryByTestId('first')).toBeVisible();
  expect(await screen.queryByTestId('second')).toBeVisible();
  expect(await screen.queryByTestId('third')).toBe(null);
});
