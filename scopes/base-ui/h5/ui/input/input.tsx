import React from 'react';

import { Input as BasicInput } from 'antd-mobile';
import type { InputProps as BasicInputProps, InputRef } from 'antd-mobile/es/components/input';
import classNames from 'classnames';

import styles from './input.module.less';

export type InputProps = {
  ellipsis?: boolean;
  precision?: number;
} & BasicInputProps;

export const Input = React.forwardRef<InputRef, InputProps>(
  ({ ellipsis = true, precision, className, onChange, onBlur, ...props }, ref) => {
    const { max, min } = props;
    return (
      <BasicInput
        ref={ref}
        className={classNames(ellipsis && styles.ellipsisInput, className)}
        {...props}
        //解决移动 type === 'number' 输入字符不触发onChange 展示了字符的问题
        type={props.type === 'number' ? 'text' : props.type}
        onChange={value => {
          let rawInput = value;
          if (props.type === 'number') {
            if (Number.isNaN(Number(value))) {
              //将字符串替换成空格
              rawInput = rawInput.replace(/[^0-9.]/g, '');
              const regex = /\..*?\./;
              //防止出现1.22.22 的字符串
              if (regex.test(rawInput)) {
                rawInput = rawInput.slice(0, -1);
              }
            } else {
              if (precision !== undefined && value !== null && value !== '') {
                const decimalLen = value.split('.')[1]
                  ? value.split('.')[1].length
                  : 0; /**小数位数 */
                if (precision === 0 || decimalLen > precision) {
                  /**避免无法输入.0的数值 */
                  rawInput = (
                    parseInt((Number(value) * Math.pow(10, precision)).toString()) /
                    Math.pow(10, precision)
                  ).toString();
                }
              }
            }
          }
          onChange?.(rawInput);
        }}
        onBlur={e => {
          if (props.type === 'number' && props.value) {
            if (max && Number(props.value) > max) {
              onChange?.(max.toString());
            } else if (min && Number(props.value) < min) {
              onChange?.(min.toString());
            } else if (props.value && props?.value?.toString().endsWith('.')) {
              onChange?.(props.value.slice(0, -1));
            }
          }
          onBlur?.(e);
        }}
      />
    );
  }
);
export type { InputRef };

Input.displayName = 'Input';
