import React, { useState } from 'react';

import { ImageUploader } from './image-uploader';
import type { ImageUploadItem } from './image-uploader';

const demoSrc =
  'https://images.unsplash.com/photo-1567945716310-4745a6b7844b?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=60';

export const BasicImageUploader = () => {
  const [fileList, setFileList] = useState<ImageUploadItem[]>([
    {
      url: demoSrc,
    },
  ]);

  return (
    <ImageUploader
      value={fileList}
      onChange={setFileList}
      upload={function (file: File): Promise<ImageUploadItem> {
        throw new Error('Function not implemented.');
      }}
    />
  );
};
