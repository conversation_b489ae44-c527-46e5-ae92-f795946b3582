---
description: A ImageUploader component.
---

import { ImageUploader } from './image-uploader';

A component that does something special and renders text in a div.

### Component usage

请参考 [antd-mobile ImageUploader](https://mobile.ant.design/zh/components/image-uploader) 的文档。

```js
<ImageUploader
  value={fileList}
  onChange={setFileList}
  upload={function (file: File): Promise<ImageUploadItem> {
    throw new Error('Function not implemented.');
  }}
/>
```
