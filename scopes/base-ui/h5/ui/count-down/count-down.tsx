import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import React from 'react';

import { Typography } from '@manyun/base-ui.h5.ui.typography';

export type CountDownProps = {
  value: number; // 以毫秒为单位
  format?: string;
  valueStyle?: React.CSSProperties;
  onFinish?: () => void;
  onChange?: (value: number) => void;
};

dayjs.extend(duration);

export function CountDown({
  format = 'HH:mm:ss',
  value,
  valueStyle,
  onFinish,
  onChange,
}: CountDownProps) {
  const [timeRemaining, setTimeRemaining] = React.useState(value);

  React.useEffect(() => {
    const intervalId = setInterval(() => {
      const remaining = Math.max(0, timeRemaining - 1000);
      setTimeRemaining(remaining);
      if (onChange) {
        onChange(remaining);
      }
    }, 1000);

    // 清理定时器
    return () => clearInterval(intervalId);
  }, [onChange, timeRemaining]);

  React.useEffect(() => {
    if (timeRemaining <= 0 && onFinish) {
      onFinish();
    }
  }, [timeRemaining, onFinish]);

  return (
    <Typography.Text style={{ ...valueStyle }} type="danger">
      {dayjs.duration(timeRemaining).format(format)}
    </Typography.Text>
  );
}
