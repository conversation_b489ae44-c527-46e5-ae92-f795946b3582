import React from 'react';

import { Button as BasicButton } from 'antd-mobile';
import type { ButtonProps as BasicButtonProps, ButtonRef } from 'antd-mobile/es/components/button';
import classNames from 'classnames';

import styles from './button.module.less';

export type ButtonProps = {
  size?: 'mini' | 'small' | 'middle' | 'large' | 'default';
} & Omit<BasicButtonProps, 'size'>;

export const Button = React.forwardRef<ButtonRef, ButtonProps>(
  ({ size = 'default', ...props }, ref) => {
    return (
      <BasicButton
        ref={ref}
        {...props}
        className={classNames(
          styles.default,
          size === 'large' && styles.large,
          size === 'middle' && styles.middle,
          size === 'small' && styles.small,
          size === 'mini' && styles.mini,
          props.className
        )}
      />
    );
  }
);
