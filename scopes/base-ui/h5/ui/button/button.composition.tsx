import React from 'react';

import { Space } from 'antd-mobile';

import { Button } from './button';

export const BasicButton = () => {
  return (
    <Space>
      <Button color="primary">Primary</Button>
      <Button color="success">Success</Button>
      <Button color="danger">Danger</Button>
      <Button color="warning">Warning</Button>
    </Space>
  );
};

export const BasicSizeButton = () => {
  return (
    <Space style={{ padding: '10px' }} direction="vertical">
      <h4>按钮尺寸</h4>
      <Button color="primary" size="mini">
        mini
      </Button>
      <Button color="primary" size="small">
        small
      </Button>
      <Button color="primary" size="middle">
        middle
      </Button>
      <Button color="primary" size="large">
        large
      </Button>
      <Button color="primary">default</Button>
    </Space>
  );
};
