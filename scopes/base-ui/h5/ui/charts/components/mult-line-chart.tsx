import React, { memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { useLatest } from 'react-use';

import { useThrottleFn } from 'ahooks';
import Echarts from 'echarts-for-react';

import { toRgbColorObject } from '@manyun/dc-brain.h5.util.common';

import type { BasicChartProps, BasicChartRef } from './chart.type';

export const MultiLineChart = memo(
  React.forwardRef(
    (
      { initXAxisNameIndex, data, series, onUpdateAxisPointer }: BasicChartProps,
      ref?: React.Ref<BasicChartRef | undefined>
    ) => {
      const echartsRef = useRef<Echarts>(null);
      const onUpdateAxisPointerRef = useLatest(onUpdateAxisPointer);

      const xData = useMemo(() => data.map(item => item.xAxisName) as string[], [data]);
      const _series = useMemo(
        () =>
          series.map(indicator => {
            const { r, g, b } = toRgbColorObject(indicator.color!);
            return {
              name: indicator.name,
              type: 'line',
              smooth: true,
              showSymbol: false,
              emphasis: {
                disabled: true,
              },
              data: data.map(item => item[indicator.name]),
              itemStyle: {
                color: indicator.color,
              },
              lineStyle: {
                shadowBlur: 5,
                shadowColor: `rgba(${r}, ${g}, ${b}, 0.25)`,
                shadowOffsetY: 5,
              },
            };
          }),
        [data, series]
      );
      const option = useMemo(() => {
        return {
          grid: {
            top: 4,
            left: 12,
            right: 12,
            bottom: 24,
          },
          xAxis: {
            type: 'category',
            // boundaryGap: false,
            nameLocation: 'end',
            data: xData,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: true, // 设置为 true 显示刻度
              lineStyle: {
                color: 'white', // 设置刻度线颜色
              },
            },
            axisLabel: {
              fontSize: 12,
              color: 'rgba(0, 0, 0, 0.45)',
              interval: xData.length - 2,
              margin: 12,
            },
            axisPointer: {
              show: true,
              type: 'line', // 可以是 'shadow' 或 'line'
              lineStyle: {
                type: 'solid',
                color: '#C7D7EC',
              },
              handle: {
                show: true, //@see https://echarts.apache.org/zh/option.html#xAxis.axisPointer.show
              },
              label: {
                show: false,
              },
              showSymbol: true,
            },
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: 'rgba(0, 0, 0, 0.06)',
              },
            },
            axisLabel: {
              fontSize: 12,
              color: 'rgba(0, 0, 0, 0.25)',
              inside: true,
              hideOverlap: true,
              show: false,
              showMaxLabel: true,
              showMinLabel: true,
            },
          },
          series: _series,
          tooltip: {
            show: false,
            trigger: 'axis', // 设置 tooltip 触发方式为坐标轴触发, 才能出发默认
          },
        };
      }, [_series, xData]);

      const _onUpdateAxisPointer = useCallback(
        (event: { axesInfo: { value: number; axisDim: string; axisIndex: number }[] }) => {
          const xAxisInfo = event.axesInfo[0];
          if (xAxisInfo) {
            const xAxisValue = xAxisInfo.value;
            const xAxisName = xData[xAxisValue]; // replace with your x axis data

            onUpdateAxisPointerRef.current?.(xAxisName);
          }
        },
        [onUpdateAxisPointerRef, xData]
      );

      const onReady = useCallback(
        (_initXAxisNameIndex?: number) => {
          const instance = echartsRef.current?.getEchartsInstance();
          if (instance) {
            instance.dispatchAction({
              type: 'showTip',
              seriesIndex: 0,
              dataIndex:
                _initXAxisNameIndex !== undefined && _initXAxisNameIndex > -1
                  ? _initXAxisNameIndex
                  : xData.length - 1,
            });
          }
        },
        [xData.length]
      );

      useImperativeHandle(ref, () => ({
        onResetSelected: () => {
          onReady();
        },
      }));

      useEffect(() => {
        onReady(initXAxisNameIndex);
      }, [initXAxisNameIndex, onReady]);

      const { run: throttleUpdateAxisPointer } = useThrottleFn(_onUpdateAxisPointer, { wait: 100 });

      return (
        <Echarts
          ref={echartsRef}
          option={option}
          style={{
            width: '100%',
            height: 164,
          }}
          onEvents={{
            updateAxisPointer: throttleUpdateAxisPointer,
          }}
        />
      );
    }
  )
);

MultiLineChart.displayName = 'MultiLineChart';
