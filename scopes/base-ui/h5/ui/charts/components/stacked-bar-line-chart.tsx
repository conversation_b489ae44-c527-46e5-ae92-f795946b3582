import React, { memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { useLatest } from 'react-use';

import { useThrottleFn } from 'ahooks';
import Echarts from 'echarts-for-react';

import type { BasicChartProps, BasicChartRef } from './chart.type';

export const StackedBarLineChart = memo(
  React.forwardRef(
    (
      { data, series, option, onUpdateAxisPointer }: BasicChartProps,
      ref?: React.Ref<BasicChartRef | undefined>
    ) => {
      const echartsRef = useRef<Echarts>(null);
      const xData = useMemo(() => data.map(item => item.xAxisName), [data]);
      const _series = useMemo(
        () =>
          series.map(indicator => ({
            name: indicator.name,
            type: indicator.type ?? 'bar',
            stack: indicator.type === 'line' ? 'line' : 'bar',
            smooth: true,
            showSymbol: false,
            barWidth: 6,
            emphasis: {
              disabled: true,
            },
            data: data.map(item => item[indicator.name]),
            itemStyle: {
              color: indicator.color,
            },
          })),
        [data, series]
      );
      const mergedOption = {
        grid: {
          top: 4,
          left: 12,
          right: 12,
          bottom: 22,
        },
        tooltip: {
          show: false,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          show: false,
        },
        xAxis: {
          type: 'category',
          data: xData,
          splitLine: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: true, // 设置为 true 显示刻度
            lineStyle: {
              color: 'white', // 设置刻度线颜色
            },
          },
          axisLabel: {
            fontSize: 12,
            color: 'rgba(0, 0, 0, 0.45)',
            interval: xData.length - 2,
          },
          axisPointer: {
            type: 'shadow', // 可以是 'shadow' 或 'line'
            lineStyle: {
              type: 'solid',
              color: '#C7D7EC',
            },
            handle: {
              show: true,
            },
            shadowStyle: {
              color: 'rgba(0, 181, 120, 0.05)',
            },
            label: {
              show: false,
            },
          },
        },
        yAxis: {
          type: 'value',
          splitLine: {
            lineStyle: {
              type: 'dashed',
              color: 'rgba(0, 0, 0, 0.06)',
            },
          },
          axisLabel: {
            show: false,
          },
        },
        series: _series,
        ...option,
      };

      const onReady = useCallback(() => {
        const instance = echartsRef.current?.getEchartsInstance();
        if (instance) {
          instance.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: xData.length - 1,
          });
        }
      }, [xData.length]);

      useImperativeHandle(ref, () => ({
        onResetSelected: () => {
          onReady();
        },
      }));

      useEffect(() => {
        onReady();
      }, [onReady]);

      const onUpdateAxisPointerRef = useLatest(onUpdateAxisPointer);
      const _onUpdateAxisPointer = useCallback(
        (event: { axesInfo: { value: number; axisDim: string; axisIndex: number }[] }) => {
          const xAxisInfo = event.axesInfo[0];
          if (xAxisInfo) {
            const xAxisValue = xAxisInfo.value;
            const xAxisName = xData[xAxisValue]; // replace with your x axis data
            onUpdateAxisPointerRef.current?.(xAxisName);
          }
        },
        [onUpdateAxisPointerRef, xData]
      );

      const { run: throttleUpdateAxisPointer } = useThrottleFn(_onUpdateAxisPointer, { wait: 100 });
      const onEvents = useMemo(
        () => ({
          updateAxisPointer: throttleUpdateAxisPointer,
        }),
        [throttleUpdateAxisPointer]
      );

      return (
        <Echarts
          ref={echartsRef}
          style={{
            width: '100%',
            height: 120,
            padding: 0,
          }}
          option={mergedOption}
          onEvents={onEvents}
        />
      );
    }
  )
);

StackedBarLineChart.displayName = 'StackedBarLineChart';
