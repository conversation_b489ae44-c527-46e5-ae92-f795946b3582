import React, {
  memo,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import { useLatest } from 'react-use';

import { useThrottleFn } from 'ahooks';
import Echarts from 'echarts-for-react';
import * as echarts from 'echarts/core';
import get from 'lodash.get';

import { ThemeContext } from '@manyun/dc-brain.h5.chart.theme';

import type { BasicChartProps, BasicChartRef } from './chart.type';
import { toRgbColorObject } from '@manyun/dc-brain.h5.util.common';

export const AreaLineChart = memo(
  React.forwardRef(
    (
      { data, series, onUpdateAxisPointer }: BasicChartProps,
      ref?: React.Ref<BasicChartRef | undefined>
    ) => {
      const { json } = useContext(ThemeContext);
      const colors = get(json, ['color']);
      const { r, g, b } = toRgbColorObject(colors[0]);

      const echartsRef = useRef<Echarts>(null);
      const onUpdateAxisPointerRef = useLatest(onUpdateAxisPointer);
      const xData = useMemo(() => data.map(item => item.xAxisName) as string[], [data]);

      const _series = useMemo(
        () =>
          series.map(indicator => ({
            name: indicator.name,
            type: 'line',
            smooth: 0.6,
            showSymbol: false,
            data: data.map(item => item[indicator.name]),
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: `rgba(${r}, ${g}, ${b}, 0.18)` },
                { offset: 1, color: `rgba(${r}, ${g}, ${b}, 0)` },
              ]),
            },
            itemStyle: {
              color: colors[0],
            },
          })),
        [b, colors, data, g, r, series]
      );

      const option = useMemo(() => {
        return {
          grid: {
            top: 4,
            left: '8%',
            right: '8%',
            bottom: 0,
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            data: xData,
            boundaryGap: false,
            splitLine: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false, // 设置为 true 显示刻度
              lineStyle: {
                color: 'white', // 设置刻度线颜色
              },
            },
            axisLabel: {
              fontSize: 12,
              color: 'rgba(0, 0, 0, 0.45)',
              interval: xData.length - 2,
            },
            axisPointer: {
              show: true,
              type: 'line', // 可以是 'shadow' 或 'line'
              lineStyle: {
                type: 'solid',
                color: '#C7D7EC',
              },
              handle: {
                show: true, //@see https://echarts.apache.org/zh/option.html#xAxis.axisPointer.show
              },
              label: {
                show: false,
              },
              // showSymbol: true,
            },
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
              lineStyle: {
                type: 'dashed',
                color: 'rgba(0, 0, 0, 0.06)',
              },
            },
            axisLabel: {
              fontSize: 12,
              color: 'rgba(0, 0, 0, 0.25)',
              inside: true,
              interval: 2,
              margin: -8,
              hideOverlap: true,
              show: false,
            },
          },
          series: _series,
          tooltip: {
            show: false,
            trigger: 'axis', // 设置 tooltip 触发方式为坐标轴触发, 才能出发默认
          },
        };
      }, [_series, xData]);

      const _onUpdateAxisPointer = useCallback(
        (event: { axesInfo: { value: number; axisDim: string; axisIndex: number }[] }) => {
          const xAxisInfo = event.axesInfo[0];
          if (xAxisInfo) {
            const xAxisValue = xAxisInfo.value;
            const xAxisName = xData[xAxisValue]; // replace with your x axis data
            onUpdateAxisPointerRef.current?.(xAxisName);
          }
        },
        [onUpdateAxisPointerRef, xData]
      );

      const onReady = useCallback(() => {
        const instance = echartsRef.current?.getEchartsInstance();
        if (instance) {
          instance.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: xData.length - 1,
          });
        }
      }, [xData.length]);

      useImperativeHandle(ref, () => ({
        onResetSelected: () => {
          onReady();
        },
      }));

      useEffect(() => {
        onReady();
      }, [onReady]);

      const { run: throttleUpdateAxisPointer } = useThrottleFn(_onUpdateAxisPointer, { wait: 100 });

      const onEvents = useMemo(
        () => ({
          updateAxisPointer: throttleUpdateAxisPointer,
        }),
        [throttleUpdateAxisPointer]
      );

      return (
        <Echarts
          ref={echartsRef}
          option={option}
          notMerge={false}
          style={{
            width: '100%',
            height: 164,
          }}
          onEvents={onEvents}
        />
      );
    }
  )
);

AreaLineChart.displayName = 'AreaLineChart';
