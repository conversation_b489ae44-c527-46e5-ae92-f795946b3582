import type { EChartsReactProps } from 'echarts-for-react';

export type DataItem = {
  xAxisName: string;
  [key: string]: number | string;
};

export type SeriesProperty = {
  name: string;
  color?: string;
  type?: string;
};

export type BasicChartRef = {
  onResetSelected: (xAxisName?: string) => void;
};

export type BasicChartProps = {
  initXAxisNameIndex?: number;
  data: DataItem[];
  series: SeriesProperty[];
  option?: EChartsReactProps['option'];
  onUpdateAxisPointer?: (xAxisName: string) => void;
};
