import BasicDatePickerView from 'antd-mobile/es/components/date-picker-view';
import type {
  DatePickerViewProps as BasicDatePickerViewProps,
  DatePickerFilter,
} from 'antd-mobile/es/components/date-picker-view';
import PickerView from 'antd-mobile/es/components/picker-view';
import type { PickerColumn } from 'antd-mobile/es/components/picker-view';
import moment from 'moment';
import React, { useState } from 'react';

import styles from './date-picker-view.module.less';
import { weekdayToZh } from './utils';

export type { DatePickerFilter };

export type ExtraType = 'am' | 'pm';

export type DatePickerViewProps = {
  precision?: BasicDatePickerViewProps['precision'] | 'half-day' | 'half-hour';
  defaultExtra?: string;
  onChange?: (date: Date, extra?: ExtraType) => void;
} & Omit<BasicDatePickerViewProps, 'precision' | 'onChange'>;

const amPmColumns: PickerColumn[] = [
  [
    { value: 'am', label: '上午' },
    { value: 'pm', label: '下午' },
  ],
];

export function DatePickerView({
  precision,
  defaultValue,
  defaultExtra,
  onChange,
  ...props
}: DatePickerViewProps) {
  const [pickerVal, setPickerVal] = useState<ExtraType>('am');
  const innerDefaultVal =
    precision === 'half-hour' ? moment().startOf('hour').toDate() : moment().toDate();
  const [date, setDate] = useState<Date>(defaultValue ?? innerDefaultVal);

  return (
    <>
      <div className={styles.container}>
        <BasicDatePickerView
          {...props}
          defaultValue={defaultValue ?? innerDefaultVal}
          precision={
            precision === 'half-day' ? 'day' : precision === 'half-hour' ? 'minute' : precision
          }
          renderLabel={
            precision === 'week-day' || precision === 'week' ? weekdayLabelRenderer : dateRenderer
          }
          filter={
            precision === 'half-hour' ? (props.filter ? props.filter : dateFilter) : props.filter
          }
          onChange={val => {
            setDate(val);
            if (precision === 'half-day') {
              onChange && onChange(val, pickerVal);
            } else {
              onChange && onChange(val);
            }
          }}
        />
        {precision === 'half-day' && (
          <PickerView
            defaultValue={defaultExtra ? [defaultExtra] : undefined}
            columns={amPmColumns}
            style={{ ...props.style }}
            onChange={vals => {
              const val = vals[0];
              setPickerVal(val as ExtraType);
              onChange && onChange(date, val as ExtraType);
            }}
          />
        )}
      </div>
    </>
  );
}

const dateRenderer = (type: string, data: number) => {
  switch (type) {
    case 'year':
      return data + '年';
    case 'month':
      return data + '月';
    case 'day':
      return data + '日';
    case 'hour':
      return data + '时';
    case 'minute':
      return data + '分';
    case 'second':
      return data + '秒';
    case 'quarter':
      return data + '季';
    default:
      return data;
  }
};

const weekdayLabelRenderer = (type: string, data: number) => {
  switch (type) {
    case 'year':
      return data + '年';
    case 'week':
      return data + '周';
    case 'week-day':
      return weekdayToZh(data);
    default:
      return data;
  }
};

const dateFilter: DatePickerFilter = {
  minute: (val: number) => {
    if (val === 0 || val === 30) {
      return true;
    }
    return false;
  },
};
