import type { ReactNode } from 'react';
import React from 'react';

import { Badge as BasicBadge } from 'antd-mobile';
import type { BadgeProps as BasicBadgeProps } from 'antd-mobile/es/components/badge';

export type BadgeProps = { overflowCount?: number; children?: ReactNode } & BasicBadgeProps;

export const Badge = ({ style, content, overflowCount = 99, children, ...rest }: BadgeProps) => {
  let newContent = content;
  if (content && Number(content) && Number(content) > overflowCount) {
    newContent = '...';
  }
  return (
    <BasicBadge
      style={style}
      content={
        typeof newContent === 'number' ? (newContent > 0 ? newContent : undefined) : newContent
      }
      {...rest}
    >
      {children}
    </BasicBadge>
  );
};

export const dot = <React.Fragment />;

Badge.dot = BasicBadge.dot;
