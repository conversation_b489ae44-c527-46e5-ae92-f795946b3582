@descriptions-prefix-cls: ~'adm-descriptions';

@padding-lg: 24px;
@padding-md: 16px;
@padding-sm: 12px;
@padding-xs: 8px;
@padding-xss: 4px;
@black: #000;
@heading-color: var(--adm-color-title);
@font-size-base: var(--adm-font-size-6);
@font-size-lg: var(--adm-font-size-8);
@line-height-base: 1.5715;
@border-radius-base: 2px;
@text-color: var(--adm-color-text, #000);
@border-color-split: hsv(0, 0, 94%);
@descriptions-bg: var(--adm-component-bg);
@descriptions-item-trailing-colon: true;
@descriptions-title-margin-bottom: 12px;
@descriptions-item-padding-bottom: @padding-xss;
@descriptions-item-label-colon-margin-right: 8px;
@descriptions-item-label-margin-bottom: 8px;
@descriptions-item-label-colon-margin-left: 2px;
@descriptions-default-padding: @padding-xs @padding-sm;
@descriptions-middle-padding: @padding-sm @padding-lg;
@descriptions-small-padding: @padding-xs @padding-md;
@descriptions-extra-color: @text-color;

.@{descriptions-prefix-cls} {
  &-header {
    display: flex;
    align-items: center;
    margin-bottom: @descriptions-title-margin-bottom;
  }

  &-title {
    flex: auto;
    overflow: hidden;
    color: @heading-color;
    font-weight: bold;
    font-size: @font-size-lg;
    line-height: @line-height-base;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &-extra {
    margin-left: auto;
    color: @descriptions-extra-color;
    font-size: @font-size-base;
  }

  &-view {
    width: 100%;
    overflow: hidden;
    border-radius: @border-radius-base;
    table {
      width: 100%;
      table-layout: fixed;
    }
  }

  &-row {
    > th,
    > td {
      padding-bottom: @descriptions-item-padding-bottom;
    }
    &:last-child {
      border-bottom: none;
    }
  }

  &-item-label {
    color: var(--adm-color-text-secondary);
    font-weight: normal;
    font-size: @font-size-base;
    line-height: @line-height-base;
    text-align: start;

    &::after {
      & when (@descriptions-item-trailing-colon=true) {
        content: ':';
      }
      & when not (@descriptions-item-trailing-colon=true) {
        content: ' ';
      }

      position: relative;
      top: -0.5px;
      margin: 0 @descriptions-item-label-colon-margin-right 0
        @descriptions-item-label-colon-margin-left;
    }

    &.@{descriptions-prefix-cls}-item-no-colon::after {
      content: ' ';
    }
  }

  &-item-no-label {
    &::after {
      margin: 0;
      content: '';
    }
  }

  &-item-content {
    display: table-cell;
    flex: 1;
    color: @text-color;
    font-size: @font-size-base;
    line-height: @line-height-base;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  &-item {
    padding-bottom: 0;
    vertical-align: top;

    &-container {
      display: flex;

      .@{descriptions-prefix-cls}-item-label,
      .@{descriptions-prefix-cls}-item-content {
        display: inline-flex;
        align-items: baseline;
      }
    }
    &-container-vertical {
      flex-direction: column;
      >.@{descriptions-prefix-cls}-item-label{
        margin-bottom:@descriptions-item-label-margin-bottom ;
      }
    }
  }

  &-large {
    .@{descriptions-prefix-cls}-row {
      > th,
      > td {
        padding-bottom: @padding-lg;
      }
    }
  }

  &-middle {
    .@{descriptions-prefix-cls}-row {
      > th,
      > td {
        padding-bottom: @padding-md;
      }
    }
  }

  &-small {
    .@{descriptions-prefix-cls}-row {
      > th,
      > td {
        padding-bottom: @padding-sm;
      }
    }
  }
 
  &-x-small {
    .@{descriptions-prefix-cls}-row {
      > th,
      > td {
        padding-bottom: @padding-xs;
      }
    }
  }

  &-xs-small {
    .@{descriptions-prefix-cls}-row {
      > th,
      > td {
        padding-bottom: @padding-xss;
      }
    }
  }


  &-bordered {
    .@{descriptions-prefix-cls}-view {
      border: 1px solid @border-color-split;
      > table {
        table-layout: auto;
        border-collapse: collapse;
      }
    }

    .@{descriptions-prefix-cls}-item-label,
    .@{descriptions-prefix-cls}-item-content {
      padding: @descriptions-default-padding;
      border-right: 1px solid @border-color-split;

      &:last-child {
        border-right: none;
      }
    }

    .@{descriptions-prefix-cls}-item-label {
      background-color: @descriptions-bg;
      &::after {
        display: none;
      }
    }

    .@{descriptions-prefix-cls}-row {
      border-bottom: 1px solid @border-color-split;
      &:last-child {
        border-bottom: none;
      }
    }

    &.@{descriptions-prefix-cls}-middle {
      .@{descriptions-prefix-cls}-item-label,
      .@{descriptions-prefix-cls}-item-content {
        padding: @descriptions-middle-padding;
      }
    }

    &.@{descriptions-prefix-cls}-small {
      .@{descriptions-prefix-cls}-item-label,
      .@{descriptions-prefix-cls}-item-content {
        padding: @descriptions-small-padding;
      }
    }
  }

  &-vertical {
    > .@{descriptions-prefix-cls}-view {
      > table {
        > tbody {
          > tr {
            > .@{descriptions-prefix-cls}-item {
              > .@{descriptions-prefix-cls}-item-container {
                > .@{descriptions-prefix-cls}-item-content {
                  font-size: @font-size-lg;
                }
              }
            }
          }
        }
      }
    }
  }
}
