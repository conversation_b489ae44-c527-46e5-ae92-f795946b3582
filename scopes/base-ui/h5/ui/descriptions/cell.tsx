import classNames from 'classnames';
import * as React from 'react';

import './descriptions.less';

function notEmpty(val: unknown) {
  return val !== undefined && val !== null;
}

export interface CellProps {
  itemPrefixCls: string;
  span: number;
  className?: string;
  component: string;
  style?: React.CSSProperties;
  labelStyle?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  bordered?: boolean;
  label?: React.ReactNode;
  content?: React.ReactNode;
  colon?: boolean;
  layout?: 'horizontal' | 'vertical';
}

const Cell: React.FC<CellProps> = ({
  itemPrefixCls,
  component,
  span,
  className,
  style,
  labelStyle,
  contentStyle,
  bordered,
  label,
  content,
  colon,
  layout,
}) => {
  const Component = component as any;

  if (bordered) {
    return (
      <Component
        className={classNames(
          notEmpty(label) && `${itemPrefixCls}-item-label`,
          notEmpty(content) && `${itemPrefixCls}-item-content`,
          className
        )}
        style={style}
        colSpan={span}
      >
        {notEmpty(label) && <span style={labelStyle}>{label}</span>}
        {notEmpty(content) && <span style={contentStyle}>{content}</span>}
      </Component>
    );
  }

  return (
    <Component
      className={classNames(`${itemPrefixCls}-item`, className)}
      style={style}
      colSpan={span}
    >
      <div
        className={classNames(
          `${itemPrefixCls}-item-container`,
          layout === 'vertical' && `${itemPrefixCls}-item-container-vertical`
        )}
      >
        {label && (
          <div
            className={classNames(
              `${itemPrefixCls}-item-label`,
              !colon && `${itemPrefixCls}-item-no-colon`
            )}
            style={labelStyle}
          >
            {label}
          </div>
        )}
        {content && (
          <span className={classNames(`${itemPrefixCls}-item-content`)} style={contentStyle}>
            {content}
          </span>
        )}
      </div>
    </Component>
  );
};

export default Cell;
