import React from 'react';

import { ThemeProvider } from '@manyun/base-ui.h5.theme.theme';

import { Descriptions } from './descriptions';

export const BasicDescriptions = () => {
  return (
    <Descriptions bordered colon={false}>
      <Descriptions.Item label="UserName"><PERSON></Descriptions.Item>
      <Descriptions.Item label="Telephone">**********</Descriptions.Item>
      <Descriptions.Item label="Live">Hangzhou, Zhejiang</Descriptions.Item>
      <Descriptions.Item label="Remark">empty</Descriptions.Item>
      <Descriptions.Item label="Address">
        No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
      </Descriptions.Item>
    </Descriptions>
  );
};

export const Basic2Descriptions = () => {
  return (
    <ThemeProvider theme="red" mode="dark">
      <Descriptions layout="vertical" colon={false}>
        <Descriptions.Item label="UserName"><PERSON></Descriptions.Item>
        <Descriptions.Item label="Telephone">**********</Descriptions.Item>
        <Descriptions.Item label="Live">Hangzhou, Zhejiang</Descriptions.Item>
        <Descriptions.Item label="Remark">empty</Descriptions.Item>
        <Descriptions.Item label="Address">
          No. 18, Wantang Road, Xihu District, Hangzhou, Zhejiang, China
        </Descriptions.Item>
      </Descriptions>
    </ThemeProvider>
  );
};
