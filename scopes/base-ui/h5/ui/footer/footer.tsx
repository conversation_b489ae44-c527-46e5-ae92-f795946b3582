import React, { type ReactNode } from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import type { BlockProps } from '@manyun/base-ui.h5.ui.block';
import { SafeArea } from '@manyun/base-ui.h5.ui.safe-area';

import styles from './footer.module.less';

export type FooterProps = {
  children?: ReactNode;
  safeArea?: boolean;
} & BlockProps;

export function Footer({ children, safeArea, ...rest }: FooterProps) {
  return (
    <Block className={styles.footer} {...rest}>
      {children}
      {safeArea && <SafeArea position="bottom" />}
    </Block>
  );
}
