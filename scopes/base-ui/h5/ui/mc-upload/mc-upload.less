@class-prefix-file-upload: ~'adm-file-upload';

.@{class-prefix-file-upload} {
  &-space {
    --gap: 10px;
  }

  &-upload-button-wrap {
    position: relative;

    .@{class-prefix-file-upload}-upload-button {
      background-color: #f2f6fb;
      text-align: center;
      line-height: var(--cell-size);
      display: block;
      width: 40px;
      height: 40px;
      line-height: 40px;

      &-icon {
        color: var(--adm-color-text);
        font-size: 16px;
      }
    }
    .@{class-prefix-file-upload}-input {
      cursor: pointer;
      position: absolute;
      opacity: 0;
      left: 0;
      top: 0;
      width: 50px;
      height: 50px;
      border-radius: 4px;
    }
  }

  &-cell {
    position: relative;
    width: var(--cell-size);
    height: var(--cell-size);
    border-radius: 4px;
    overflow: hidden;

    &-fail {
      border: red solid 1px;
      box-sizing: border-box;
    }

    &-delete {
      position: absolute;
      top: 0;
      right: 0;
      width: 14px;
      height: 14px;
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 0 0 0 12px;
      font-size: 8px;
      color: var(--adm-color-white);
      cursor: pointer;
      &-icon {
        position: absolute;
        left: 4px;
        top: 3px;
      }
    }

    &-mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      color: #fff;
      background-color: rgba(50, 50, 51, 0.88);
    }

    &-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      box-sizing: border-box;
      padding-top: 8px;
    }

    &-mask-message {
      display: inline-block;
      padding: 6px 4px;
      font-size: 12px;
    }

    &-image {
      width: var(--cell-size);
      height: var(--cell-size);
    }
  }
}
