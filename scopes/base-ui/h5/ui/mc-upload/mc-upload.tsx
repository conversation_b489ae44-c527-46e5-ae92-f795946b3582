import PlusOutlined from '@ant-design/icons/PlusOutlined';
import { Toast } from 'antd-mobile';
import type { ImageUploaderProps } from 'antd-mobile/es/components/image-uploader';
import classNames from 'classnames';
import type { ReactNode } from 'react';
import React, { useEffect, useRef, useState } from 'react';
import { useLatest } from 'react-use';

import { PreviewItem } from '@manyun/base-ui.h5.ui.file-preview';
import type { PreviewFile, PreviewFileStatus } from '@manyun/base-ui.h5.ui.file-preview';
import { Space } from '@manyun/base-ui.h5.ui.space';
import type { McUploadFile } from '@manyun/base-ui.model.mc-upload-file';
import { uploadFileWeb as uploadFile } from '@manyun/base-ui.service.dcom.upload-file';

import { attrAccept } from './attr-accept';
import './mc-upload.less';
import { useRequest } from './use-request';
import { useIsomorphicLayoutEffect, useMemoizedFn, usePropsValue } from './utils';

const classPrefix = `adm-file-upload`;
const classPrefixPreview = `adm-file-preview`;

export type McUploadProps = {
  /**业务id */
  targetId?: string;
  /**业务类型 */
  targetType?: string;
  /** 单个文件大小限制（单位：MB） */
  maxFileSize?: number;
  value?: McUploadFile[];
  children?: ReactNode;
  onChange?: (files: McUploadFile[]) => void;
} & Omit<FileUploaderProps, 'upload' | 'value' | 'onChange'>;

export function McUpload({
  targetId,
  targetType,
  maxFileSize = 100,
  children,
  value,
  onChange,
  onCountExceed,
  accept = 'image/*',
  ...props
}: McUploadProps) {
  const { fetchBizFiles, deleteBizFile, saveBizFiles } = useRequest({
    targetId: targetId,
    targetType: targetType,
  });
  const [thumbFiles, setThumbFiles] = useState<McUploadFile[]>(value ?? []);
  const idCountRef = useRef(0);
  const onChangeRef = useLatest(onChange);

  const beforeUpload = (file: File, _files: File[]) => {
    if (!attrAccept(file, accept!)) {
      Toast.show('文件格式错误');
      return null;
    }
    return checkFileSizeLimit(maxFileSize, file);
  };

  const onUpload = async (file: File) => {
    const fd = new FormData();
    fd.append('file', file);
    const { error, data } = await uploadFile(fd);
    if (error) {
      Toast.show(error.message);
      return Promise.reject(new Error(error.message));
    }
    const uploadedFile: McUploadFile = data![0];
    const key = idCountRef.current++;
    uploadedFile.id = key;
    thumbFiles.push(uploadedFile);
    const previewFile: PreviewFile = {
      key,
      id: uploadedFile.id!,
      ext: uploadedFile.ext!,
      src: uploadedFile.src,
      name: uploadedFile.name,
      size: uploadedFile.size!,
    };
    if (targetId && targetType) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      saveBizFiles([uploadedFile as any]);
    }

    return previewFile;
  };

  useEffect(() => {
    setThumbFiles(value ?? []);
  }, [value]);

  React.useEffect(() => {
    if (targetId && targetType) {
      fetchBizFiles(files => {
        if (files.length > 0) {
          onChangeRef.current?.(files as McUploadFile[]);
        }
      });
    }
  }, [fetchBizFiles, onChangeRef, targetId, targetType]);

  return (
    <FileUploader
      {...props}
      accept={accept}
      beforeUpload={beforeUpload}
      value={
        value &&
        value.map(v => ({
          key: v.id!,
          src: v.src!,
          id: v.id!,
          ext: v.ext!,
          name: v.name,
          size: v.size!,
        }))
      }
      upload={function (file: File): Promise<PreviewFile> {
        return onUpload(file);
      }}
      onChange={files => {
        const mcFiles: McUploadFile[] = [];
        files.forEach(file => {
          const mcFile = thumbFiles.find(mf => mf.id === file.key);
          if (mcFile) {
            mcFiles.push(mcFile);
          }
        });
        onChange && onChange(mcFiles);
      }}
      onCountExceed={exceed => {
        Toast.show(`最多选择 ${props.maxCount} 个文件`);
        onCountExceed && onCountExceed(exceed);
      }}
      onDelete={file => {
        const deleteMcFile = thumbFiles.find(mf => mf.id === file.key);
        if (deleteMcFile && targetId && targetType) {
          deleteBizFile({
            targetId,
            targetType,
            filePath: deleteMcFile.patialPath,
          });
        }
      }}
    >
      {children}
    </FileUploader>
  );
}

function checkFileSizeLimit(maxFileSize = Number.POSITIVE_INFINITY, file: File) {
  const fileSizeMB = file.size / 1024 / 1024;
  if (fileSizeMB > maxFileSize) {
    Toast.show(`上传文件不得超过 ${maxFileSize}MB`);
    return null;
  }
  return file;
}

type Task = {
  id: number;
  src?: string;
  file: File;
  status: PreviewFileStatus;
};

export type FileUploaderProps = Omit<
  ImageUploaderProps,
  'defaultValue' | 'upload' | 'onDelete' | 'value' | 'onChange'
> & {
  defaultValue?: PreviewFile[];
  value?: PreviewFile[];
  children?: React.ReactNode;
  deleteIconStyle?: React.CSSProperties;
  upload: (file: File) => Promise<PreviewFile>;
  onChange?: (items: PreviewFile[]) => void;
  onDelete?: (item: PreviewFile) => boolean | Promise<boolean> | void;
};
export function FileUploader({
  accept = 'image/*',
  maxCount = 0,
  showUpload = true,
  defaultValue = [],
  showFailed = false,
  ...props
}: FileUploaderProps) {
  const [value, setValue] = usePropsValue({
    defaultValue: defaultValue,
    value: props.value,
    onChange: props.onChange,
  });
  const [tasks, setTasks] = useState<Task[]>([]);
  const isShowUpload = showUpload && (maxCount === 0 || value.length + tasks.length < maxCount);
  const updateValue = useMemoizedFn((updater: (prev: PreviewFile[]) => PreviewFile[]) => {
    setValue(updater(value));
  });

  const idCountRef = useRef(0);

  async function processFile(file: File, fileList: File[]) {
    const { beforeUpload } = props;

    let transformedFile: File | null | undefined = file;

    transformedFile = await beforeUpload?.(file, fileList);

    return transformedFile;
  }

  async function onChange(e: React.ChangeEvent<HTMLInputElement>) {
    e.persist();
    const { files: rawFiles } = e.target;
    if (!rawFiles) {
      return;
    }
    let files = [].slice.call(rawFiles) as File[];
    e.target.value = ''; // HACK: fix the same file doesn't trigger onChange

    if (props.beforeUpload) {
      const postFiles = files.map(file => {
        return processFile(file, files);
      });
      await Promise.all(postFiles).then(filesList => {
        files = filesList.filter(Boolean) as File[];
      });
    }

    if (files.length === 0) {
      return;
    }

    if (maxCount > 0) {
      const exceed = (value || []).length + files.length - maxCount;
      if (exceed > 0) {
        files = files.slice(0, files.length - exceed);
        props.onCountExceed?.(exceed);
      }
    }

    const newTasks = files.map(
      file =>
        ({
          id: idCountRef.current++,
          status: 'pending',
          file,
        } as Task)
    );

    setTasks(prev => [...prev, ...newTasks]);

    await Promise.all(
      newTasks.map(async currentTask => {
        try {
          const result = await props.upload(currentTask.file);
          setTasks(prev => {
            return prev.map(task => {
              if (task.id === currentTask.id) {
                return {
                  ...task,
                  src: result.src,
                };
              }
              return task;
            });
          });
          updateValue(prev => {
            const newVal = { ...result };
            return [...prev, newVal];
          });
        } catch (e) {
          setTasks(prev => {
            return prev.map(task => {
              if (task.id === currentTask.id) {
                return {
                  ...task,
                  status: 'fail',
                };
              }
              return task;
            });
          });
          throw e;
        }
      })
    ).catch(error => console.error(error));
  }

  useIsomorphicLayoutEffect(() => {
    setTasks(prev =>
      prev.filter(task => {
        if (task.src === undefined) {
          return true;
        }
        return !value.some(fileItem => fileItem.src === task.src);
      })
    );
  }, [value]);

  return (
    <div className={classNames(classPrefixPreview, classPrefix)}>
      <Space className={`${classPrefix}-space`} direction="vertical" block>
        {value.map((fileItem, index) => (
          <PreviewItem
            key={fileItem.key ?? index}
            url={fileItem.thumbnailUrl ?? fileItem.src}
            file={fileItem}
            showPreview
            deletable
            deleteIconStyle={props?.deleteIconStyle}
            onDelete={async () => {
              const canDelete = await props.onDelete?.(fileItem);
              if (canDelete === false) {
                return;
              }
              setValue(value.filter((x, i) => i !== index));
            }}
          />
        ))}
        {tasks.map(task => {
          if (!showFailed && task.status === 'fail') {
            return null;
          }
          return (
            <PreviewItem
              key={task.id}
              file={{
                id: task.id,
                src: URL.createObjectURL(task.file),
                ext: task.file.type,
                size: task.file.size,
                name: task.file.name,
                status: task.status,
              }}
              showPreview={false}
              deletable={task.status !== 'pending'}
              onDelete={() => {
                setTasks(tasks.filter(x => x.id !== task.id));
              }}
            />
          );
        })}
        {isShowUpload && (
          <div className={`${classPrefix}-upload-button-wrap`}>
            {props.children ? (
              props.children
            ) : (
              <span className={`${classPrefix}-cell ${classPrefix}-upload-button`} role="button">
                <span className={`${classPrefix}-upload-button-icon`}>
                  <PlusOutlined />
                </span>
              </span>
            )}
            {!props.disableUpload && (
              <input
                accept={accept}
                capture={props.capture}
                multiple={props.multiple}
                type="file"
                className={`${classPrefix}-input`}
                onChange={onChange}
              />
            )}
          </div>
        )}
      </Space>
    </div>
  );
}
