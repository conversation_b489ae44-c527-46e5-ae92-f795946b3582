import React from 'react';

import { destroyMock, webRequest } from '@manyun/service.request';

import { McUpload } from './mc-upload';

export const BasicMcUpload = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
    return () => {
      // mockOff();
    };
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <McUpload
      multiple
      maxFileSize={20}
      // value={[
      //   {
      //     url: 'https://images.unsplash.com/photo-1567945716310-4745a6b7844b?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=300&q=60',
      //     /**
      //      * 完整的uploadfile
      //      */
      //   },
      // ]}
      onChange={files => {
        // console.log(files);
      }}
      accept=".pdf,image/*"
    ></McUpload>
  );
};
