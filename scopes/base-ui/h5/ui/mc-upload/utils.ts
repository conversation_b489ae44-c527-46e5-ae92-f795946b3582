import { useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';

/**useUpdate */
export const useUpdate = () => {
  const [, setState] = useState(0);

  return useCallback(() => setState((num: number): number => num + 1), []);
};
/**  end useUpdate */

type Options<T> = {
  value?: T;
  defaultValue: T;
  onChange?: (v: T) => void;
};

export function usePropsValue<T>(options: Options<T>) {
  const { value, defaultValue, onChange } = options;

  const update = useUpdate();

  const stateRef = useRef<T>(value !== undefined ? value : defaultValue);
  if (value !== undefined) {
    stateRef.current = value;
  }

  const setState = useMemoizedFn((v: T) => {
    if (value === undefined) {
      stateRef.current = v;
      update();
    }
    onChange?.(v);
  });
  return [stateRef.current, setState] as const;
}

/** useMemoizedFn */
type noop = (this: any, ...args: any[]) => any;

type PickFunction<T extends noop> = (
  this: ThisParameterType<T>,
  ...args: Parameters<T>
) => ReturnType<T>;

export function useMemoizedFn<T extends noop>(fn: T) {
  if (process.env.NODE_ENV === 'development') {
    if (typeof fn !== 'function') {
      console.error(`useMemoizedFn expected parameter is a function, got ${typeof fn}`);
    }
  }

  const fnRef = useRef<T>(fn);

  // why not write `fnRef.current = fn`?
  // https://github.com/alibaba/hooks/issues/728
  fnRef.current = useMemo(() => fn, [fn]);

  const memoizedFn = useRef<PickFunction<T>>();
  if (!memoizedFn.current) {
    memoizedFn.current = function (this, ...args) {
      return fnRef.current.apply(this, args);
    };
  }

  return memoizedFn.current;
}
/**end useMemoizedFn */

/** useIsomorphicLayoutEffect */
const isBrowser = !!(
  typeof window !== 'undefined' &&
  window.document &&
  window.document.createElement
);
export const useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;
/** end useIsomorphicLayoutEffect */
