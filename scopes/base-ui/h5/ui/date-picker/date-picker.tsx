import React, { useEffect, useState } from 'react';

import dayjs from 'dayjs';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { DatePickerView } from '@manyun/base-ui.h5.ui.date-picker-view';
import type { DatePickerViewProps } from '@manyun/base-ui.h5.ui.date-picker-view';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import type { PopupProps, PopupRef } from '@manyun/base-ui.h5.ui.popup';
import { Space } from '@manyun/base-ui.h5.ui.space';

export type { DatePickerRef } from 'antd-mobile';

export type DatePickerProps = {
  title?: string;
  children?: (value: Date | undefined, extra?: string) => React.ReactNode;
  renderExtraFooter?: (callback: (date: Date) => void) => React.ReactNode;
  onConfirm?: (date: Date, extra?: string) => void;
} & Omit<DatePickerViewProps, 'onChange' | 'children'> &
  Omit<PopupProps, 'children'>;

export const DatePicker = React.forwardRef<PopupRef, DatePickerProps>(
  (
    {
      title,
      onConfirm,
      defaultValue = new Date(),
      value = defaultValue,
      defaultExtra,
      renderExtraFooter,
      ...restProps
    },
    ref
  ) => {
    const [innerValue, setInnerValue] = useState<Date>(value);
    const [innerExtra, setInnerExtra] = useState<string | undefined>(defaultExtra);

    useEffect(() => {
      setInnerValue(dayjs(value).toDate());
    }, [value]);

    return (
      <>
        <Popup
          ref={ref}
          title={title}
          bodyStyle={{ height: 'auto' }}
          contentStyle={{ padding: '10px  0px 1px 0' }}
          footer
          arrow
          destroyOnClose
          onVisible={() => {
            setInnerValue(dayjs(value).toDate());
          }}
          onOK={() => {
            onConfirm?.(innerValue, innerExtra);
          }}
        >
          <Block style={{ padding: '16px 10px' }}>
            <Space style={{ width: '100%', '--gap-vertical': '16px' }} direction="vertical">
              <DatePickerView
                {...restProps}
                value={innerValue}
                onChange={(value, extra) => {
                  setInnerValue(value);
                  setInnerExtra(extra);
                }}
              />
              {renderExtraFooter &&
                renderExtraFooter(date => {
                  setInnerValue(date);
                })}
            </Space>
          </Block>
        </Popup>
        {restProps.children?.(value)}
      </>
    );
  }
);
DatePicker.displayName = 'DatePicker';
