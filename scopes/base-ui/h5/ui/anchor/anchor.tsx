import { useThrottleFn } from 'ahooks';
import debounce from 'lodash.debounce';
import throttle from 'lodash.throttle';
import React from 'react';
import { useLatest } from 'react-use';

import type { TabsProps } from '@manyun/base-ui.h5.ui.tabs';

import { StyledCustomerTabs } from './style.js';

export type AnchorProps = {
  style?: React.CSSProperties;
  mode?: 'default' | 'card';
  offsetTop?: number;
  items: { title: string; key: string }[];
  /**
   * 指定滚动的容器，默认值 window
   */
  getContainer?: () => HTMLElement | null;
  onChange?: (key: string) => void;
};

export function Anchor({
  mode = 'default',
  style,
  items,
  offsetTop = 0,
  getContainer,
  onChange,
}: AnchorProps) {
  const onChangeRef = useLatest(onChange);
  const [activeLink, setActiveLink] = React.useState<string | null>(items[0]?.key);
  const scrollContainer = React.useMemo(
    () => (getContainer ? getContainer() ?? window : window),
    [getContainer]
  );
  const [isUserClick, setIsUserClick] = React.useState(false);

  const { run: throttledOnChange } = useThrottleFn(
    (key: string) => {
      onChangeRef.current?.(key);
    },
    { wait: 100 }
  );

  React.useEffect(() => {
    const throttleHandleScroll = throttle(function handleScroll() {
      if (isUserClick) {
        setIsUserClick(false);
        return;
      }
      let newActiveLink: string | null = null;
      for (const item of items) {
        const element = document.getElementById(item.key);
        if (element && scrollContainer !== window) {
          const containerTop = (scrollContainer as HTMLElement).scrollTop;
          const elementTop = element.offsetTop - (scrollContainer as HTMLElement).offsetTop;

          if (containerTop + offsetTop >= elementTop - 1) {
            newActiveLink = item.key;
          }
        } else if (element) {
          const elementTop = element.offsetTop;
          if (window.scrollY + offsetTop >= elementTop - 1) {
            newActiveLink = item.key;
          }
        }
      }

      if (newActiveLink && newActiveLink !== activeLink) {
        setActiveLink(newActiveLink);
        throttledOnChange?.(newActiveLink);
      }
    }, 200);

    if (scrollContainer === window) {
      window.addEventListener('scroll', throttleHandleScroll);
    } else {
      scrollContainer.addEventListener('scroll', throttleHandleScroll);
    }

    return () => {
      if (scrollContainer === window) {
        window.removeEventListener('scroll', throttleHandleScroll);
      } else {
        scrollContainer.removeEventListener('scroll', throttleHandleScroll);
      }
      throttleHandleScroll.cancel();
    };
  }, [items, offsetTop, activeLink, scrollContainer, isUserClick, throttledOnChange]);

  return (
    <CustomerTabs
      style={{
        color: 'var( --adm-color-title)',
        '--title-font-size': '14px',
        ...style,
      }}
      defaultActiveKey={items[0].key}
      activeKey={activeLink}
      items={items}
      onChange={key => {
        setIsUserClick(true);
        setActiveLink(key);
        document.getElementById(`${key}`)?.scrollIntoView({ behavior: 'smooth', block: 'start' });
        onChangeRef.current?.(key);
      }}
    />
  );
}

const CustomerTabs = ({
  defaultActiveKey,
  activeKey,
  style,
  items,
  children,
  onChange,
}: TabsProps & { items: { key: string; title: React.ReactNode }[] }) => {
  const [innerActiveKey, setActiveKey] = React.useState(
    defaultActiveKey ?? activeKey ?? items[0].key
  );

  React.useEffect(() => {
    if (activeKey && activeKey !== innerActiveKey) {
      setActiveKey(activeKey);
    }
  }, [activeKey, innerActiveKey]);

  return (
    <StyledCustomerTabs>
      <div className="tabsContainer" style={style}>
        {items.map(tab => (
          <div
            key={tab.key}
            className={`tab_item ${innerActiveKey === tab.key ? 'active' : 'unActive'}`}
            onClick={event => {
              event.preventDefault();
              setActiveKey(tab.key);
              onChange?.(tab.key);
            }}
          >
            <div className="tab_item_title">{tab.title}</div>
          </div>
        ))}
      </div>
      {children}
    </StyledCustomerTabs>
  );
};
