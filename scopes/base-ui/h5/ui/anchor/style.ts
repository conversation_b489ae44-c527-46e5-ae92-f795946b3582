import styled from 'styled-components';

const TAB_ITEM_BG = '#ffffff';
const TAB_HEIGHT = '30px';
export const CONTAINER_BG = '#F2FCF8';

export const StyledCustomerTabs = styled.div`
  .tabsContainer {
    display: flex;
    background-color: ${CONTAINER_BG};
    overflow: hidden;
    display: flex;
    box-sizing: content-box;
  }
  .tab_item {
    flex: 1;
    height: ${TAB_HEIGHT};
    line-height: ${TAB_HEIGHT};
    box-sizing: border-box;
    position: relative;
    text-align: center;
    color: var(--adm-color-title);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-items: center;
    justify-content: center;
    z-index: 1;
  }
  .tab_item.unActive {
    box-shadow: 4px -4px 20px 0px #00b5791a inset;
    box-shadow: -2px 2px 4px 0px #00b57905 inset;
  }
  .tab_item.active {
    opacity: 1;
    background: ${TAB_ITEM_BG};
    border-radius: 16px 16px 0 0;
    box-shadow: 16px 16px 0 0 ${TAB_ITEM_BG}, -16px 16px 0 0 ${TAB_ITEM_BG};
    color: var(--adm-color-primary);
    font-weight: 600;
    transition: all 0.1s ease;

    .tab_item_title {
      position: relative;
    }
    .tab_item_title::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 6px;
      bottom: 3px;
      left: 0;
      background: radial-gradient(
        50% 50% at 50% 50%,
        rgba(0, 181, 120, 0.45) 0%,
        rgba(0, 181, 120, 0.05) 100%
      );

      backdrop-filter: blur(4px);
      border-radius: 3px;
    }
  }
  .tab_item.active:first-child {
    border-top-left-radius: 0;
  }

  .tab_item.active:last-child {
    border-top-right-radius: 0;
  }

  .tab_item.active::before {
    content: '';
    position: absolute;
    left: -16px;
    bottom: 0;
    width: 16px;
    height: ${TAB_HEIGHT};
    background-color: ${CONTAINER_BG};
    border-radius: 0 0 16px 0;
  }
  .tab_item.active::after {
    content: '';
    position: absolute;
    right: -16px;
    bottom: 0;
    width: 16px;
    height: ${TAB_HEIGHT};
    background: ${CONTAINER_BG};
    border-radius: 0 0 0 16px;
  }
`;
