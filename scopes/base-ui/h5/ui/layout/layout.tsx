import { useLocation } from '@tanstack/react-location';
import classNames from 'classnames';
import React from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import type { NavBarProps } from '@manyun/base-ui.h5.ui.nav-bar';
import { NavBar } from '@manyun/base-ui.h5.ui.nav-bar';
import { SafeArea } from '@manyun/base-ui.h5.ui.safe-area';
import { TENANT_ID_IS_YG, platformIsFeiShu } from '@manyun/dc-brain.h5.util.common';

import './layout.less';

export type BasicLayoutProps = {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  showBackground?: boolean;
};

export const Layout = ({ className, style, children }: BasicLayoutProps) => {
  return (
    <div className={classNames('layout', className)} style={style}>
      {children}
    </div>
  );
};

export const Header = ({
  titleAlign,
  className,
  ...props
}: NavBarProps & { titleAlign?: 'center' | 'left' | 'right' }) => {
  const location = useLocation();
  const isWhite = React.useMemo(() => platformIsFeiShu || TENANT_ID_IS_YG, []);
  return (
    <div
      className="layout-header"
      style={{
        background: isWhite
          ? 'var(--adm-color-white)'
          : 'linear-gradient(-45deg, #3dd33c 0%, #0db26d 100%)',
        color: isWhite ? `var(--adm-color-title)` : 'var(--adm-color-white)',
        ...props.style,
      }}
    >
      <SafeArea position="top" />
      <NavBar
        className={classNames(className, titleAlign && `navbar-title-${titleAlign}`)}
        {...props}
        onBack={() => {
          if (props.onBack) {
            props.onBack();
          } else {
            location.history.back();
          }
        }}
      >
        {props.children}
      </NavBar>
    </div>
  );
};

export const Content = ({ className, style, children }: BasicLayoutProps) => {
  return (
    <div style={style} className={classNames('layout-content', className)}>
      {children}
    </div>
  );
};

export const Footer = ({ className, style, children }: BasicLayoutProps) => {
  return (
    <Block style={style} className={classNames('layout-footer', className)} size="middle">
      {children}
      <SafeArea position="bottom" />
    </Block>
  );
};

export default Layout;
