import dayjs from 'dayjs';
import React from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { Descriptions } from '@manyun/base-ui.h5.ui.descriptions';
import { Divider } from '@manyun/base-ui.h5.ui.divider';
import { Empty } from '@manyun/base-ui.h5.ui.empty';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { useSpace } from '@manyun/resource-hub.gql.client.spaces';
import { fetchPagedVisitsRecords } from '@manyun/sentry.service.fetch-paged-visits-records';

export function RegistrationDetailList({
  taskNo,
  bizTag,
  registrationDetailVisible,
  setRegistrationDetailVisible,
}: {
  taskNo: string;
  bizTag: string;
  registrationDetailVisible: boolean;
  setRegistrationDetailVisible: (visible: boolean) => void;
}) {
  const [state, setState] = React.useState<any>({ list: [], blockList: [] });

  React.useEffect(() => {
    if (registrationDetailVisible && bizTag && taskNo) {
      (async () => {
        const { data, error } = await fetchPagedVisitsRecords({
          taskNo,
          bizTag,
          page: 1,
          pageSize: 500,
        });
        if (error) {
          return;
        }
        if (data.data) {
          setState({
            list: data.data || [],
            blockList: groupByBlockGuid(data.data || []),
          });
        }
      })();
    }
  }, [taskNo, registrationDetailVisible]);

  console.log(registrationDetailVisible, taskNo, bizTag, state, '121323');
  return (
    <Popup
      title="登记记录"
      visible={registrationDetailVisible}
      onMaskClick={() => {
        setRegistrationDetailVisible(false);
      }}
      footer={null}
      getContainer={null}
      contentStyle={{ padding: '0', backgroundColor: '#FFF' }}
    >
      <Block
        style={{
          padding: '0',
        }}
      >
        <>
          <div
            style={{
              display: 'flex',
              padding: '14px 16px',
              flexFlow: 'nowrap',
              height: '54px',
              background: 'rgba(248, 249, 252, 1)',
              overflowX: 'auto',
            }}
          >
            {state.blockList?.map((i: any) => {
              return (
                <Space
                  direction="vertical"
                  style={{
                    flexShrink: 0,
                    minWidth: '36px',
                    height: '50px',
                    '--gap': '0px',
                    marginRight: '12px',
                  }}
                >
                  <Typography.Text style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.45)' }}>
                    <Space size={4} wrap>
                      <SpaceText guid={i.blockGuid} />
                    </Space>
                  </Typography.Text>

                  <div style={{ display: 'flex', position: 'relative' }}>
                    <Typography.Title style={{ fontSize: '20px', marginRight: '2px' }}>
                      {i.registerNum}
                    </Typography.Title>
                    <Typography.Text
                      style={{ position: 'relative', top: '6px', color: 'rgba(0, 0, 0, 0.45)' }}
                    >
                      人
                    </Typography.Text>
                  </div>
                </Space>
              );
            })}
          </div>

          {state.list?.length >= 1 ? (
            <div>
              {state.list.map((item: any) => (
                <div key={item.id}>
                  <div style={{ padding: '16px' }}>
                    <Descriptions column={1} colon={false} layout="horizontal" size="middle">
                      <Descriptions.Item key={item.id} label="登记区域：" layout="horizontal">
                        <SpaceText guid={item.blockGuid} />
                      </Descriptions.Item>
                      <Descriptions.Item key={item.id} label="登记时间：" layout="horizontal">
                        {item.enterTime && dayjs(item.enterTime).format('YYYY-MM-DD HH:mm')}
                      </Descriptions.Item>
                      <Descriptions.Item key={item.id} label="登记人数：" layout="horizontal">
                        {item.registerNum}
                      </Descriptions.Item>
                    </Descriptions>
                  </div>
                  <Divider style={{ margin: '0 2px' }} />
                </div>
              ))}
            </div>
          ) : (
            <>
              <Empty description="暂无数据" />
            </>
          )}
        </>
      </Block>
    </Popup>
  );
}

type Item = {
  blockGuid: string;
  registerNum: number;
  [key: string]: any;
};

function groupByBlockGuid(data: Item[]) {
  const resultMap = new Map<string, { blockGuid: string; registerNum: number; items: Item[] }>();

  for (const item of data) {
    const { blockGuid, registerNum } = item;

    if (!resultMap.has(blockGuid)) {
      resultMap.set(blockGuid, {
        blockGuid,
        registerNum,
        items: [item],
      });
    } else {
      const group = resultMap.get(blockGuid)!;
      group.registerNum += registerNum;
      group.items.push(item);
    }
  }

  return Array.from(resultMap.values());
}

function SpaceText({ guid }: { guid: string }) {
  const [spaceIdc, spaceBlock, spaceRoom] = useSpace(guid);
  if (spaceBlock?.label) {
    return <>{spaceBlock.label}</>;
  }

  return <>{guid}</>;
}
