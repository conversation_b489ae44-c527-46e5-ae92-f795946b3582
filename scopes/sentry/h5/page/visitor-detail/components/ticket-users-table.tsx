import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';

import { Block } from '@manyun/base-ui.h5.ui.block';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { useLazyVisitorInfoStaffList } from '@manyun/bpm.gql.client.approval';

import styles from './ticket-users.module.less';

/** 工单人员信息 */
const { Text, Title } = Typography;
export const TicketUsersTable = ({ bizId }: { bizId: string }) => {
  const [getVisitorStaffList] = useLazyVisitorInfoStaffList();
  const [userList, setUserList] = useState<Record<string, any>[]>([]);
  const [userAllList, setUserAllList] = useState<Record<string, any>[]>([]);
  const [v, setV] = useState(true);
  const [isScroll, setIsScroll] = useState(false);
  const tableContainerRef = useRef<HTMLDivElement | null>(null);
  const startX = useRef<number>(0);
  const scrollLeft = useRef<number>(0);
  const isDragging = useRef<boolean>(false);

  useEffect(() => {
    getVisitorStaffList({
      variables: {
        taskNo: bizId,
      },
    }).then((data: Record<string, any>) => {
      const userList = data?.data?.visitorInfoStaffList?.data || [];

      setUserAllList(userList);
      setUserList(userList?.slice(0, 10));
    });
  }, []);

  const handleClick = () => {
    setV(false);
    setUserList(userAllList);
  };

  const handlePointerEnter = () => {
    setIsScroll(true);
  };

  const handlePointerLeaver = () => {
    setIsScroll(false);
  };

  // 通用事件处理函数，支持鼠标和触摸事件
  const handleDragStart = (
    e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
  ) => {
    isDragging.current = true;

    if ('clientX' in e) {
      // MouseEvent
      startX.current = e.clientX;
    } else {
      // TouchEvent
      startX.current = e.touches[0].clientX;
    }

    scrollLeft.current = tableContainerRef.current?.scrollLeft || 0;
  };

  const handleDragMove = (
    e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
  ) => {
    if (!isDragging.current || !tableContainerRef.current) return;

    let currentX: number;

    if ('clientX' in e) {
      // MouseEvent
      currentX = e.clientX;
    } else {
      // TouchEvent
      currentX = e.touches[0].clientX;
    }

    const walk = currentX - startX.current;
    tableContainerRef.current.scrollLeft = scrollLeft.current - walk;
  };

  const handleDragEnd = () => {
    isDragging.current = false;
  };

  return (
    <Block className={styles.ticketUserTableContainer}>
      <Title className={styles.title}>人员信息</Title>
      <div
        ref={tableContainerRef}
        className={styles.tableContainer}
        onPointerEnter={handlePointerEnter}
        onPointerLeave={handlePointerLeaver}
        onMouseEnter={handlePointerEnter}
        onMouseLeave={handlePointerLeaver}
        onMouseDown={handleDragStart}
        onMouseMove={handleDragMove}
        onMouseUp={handleDragEnd}
        onTouchStart={handleDragStart}
        onTouchMove={handleDragMove}
        onTouchEnd={handleDragEnd}
        style={{ overflowX: isScroll ? 'auto' : 'hidden' }}
      >
        <table>
          <thead>
            <tr>
              <th>姓名</th>
              <th>公司名称</th>
              <th>电话</th>
              <th>职位</th>
              <th>证件号码</th>
              <th>车牌号</th>
              <th>拍照权限</th>
            </tr>
          </thead>
          <tbody>
            {userList.map(item => {
              return (
                <tr key={item.id ?? '--'}>
                  <td>{item?.name ?? '--'}</td>
                  <td>{item?.companyName ?? '--'}</td>
                  <td>{item?.contactWay ?? '--'}</td>
                  <td>{item?.position ?? '--'}</td>
                  <td>{item?.identityNo ?? '--'}</td>
                  <td>{item?.plateNo ?? '--'}</td>
                  <td>
                    {item?.photoStartTime || item?.photoEndTime
                      ? `${
                          item.photoStartTime
                            ? dayjs(item.photoStartTime).format('YYYY-MM-DD HH:mm:ss')
                            : '--'
                        } ~ ${
                          item.photoEndTime
                            ? dayjs(item.photoEndTime).format('YYYY-MM-DD HH:mm:ss')
                            : '--'
                        }`
                      : '--'}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      {v && userAllList.length > 10 ? (
        <div className={styles.lookMore} onClick={handleClick}>
          查看更多
        </div>
      ) : null}
    </Block>
  );
};
