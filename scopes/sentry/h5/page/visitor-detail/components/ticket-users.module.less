.userInfoRow {
  > :global(.adm-space-item) {
    &:last-child {
      flex: 1;
      min-width: 0;
      word-wrap: break-word;
    }
  }
}

.ticketVisitorUser {
  width: 100%;
  align-items: center;
  > :global(.adm-space-item) {
    &:first-child {
      max-width: calc(100vw - 146px);
    }
  }
}

.ticketUserTableContainer {
  border-radius: var(--adm-radius-m);
  padding: 16px;

  .title {
    font-size: var(--adm-font-size-8);
    margin-bottom: 16px;
  }
}

.lookMore {
  height: 22px;
  margin-top: 16px;
  text-align: center;
  color: var(--adm-color-primary);
}

.tableContainer {
  width: 100%;
  //overflow-x: auto; /* 横向滚动 */
  -webkit-overflow-scrolling: touch; /* 优化移动端滚动 */

  table {
    width: max-content; /* 表格宽度由内容撑开 */
    min-width: 100%; /* 至少和容器一样宽 */
    border-collapse: collapse;
    white-space: nowrap; /* 禁止换行，确保列宽由内容决定 */
    cursor: pointer;
  }

  /* 隐藏默认滚动条（仅保留滑动时动态显示效果） */
  .table-container::-webkit-scrollbar {
    width: 0;
    height: 0;
    background: transparent;
  }

  tr {
    border-bottom: 1px solid var(--adm-color-gray-2);
    height: 48px;
  }

  th,
  td {
    padding: 12px;
    text-align: left;
    border: 0px;
    color: var(--adm-color-text);
    cursor: pointer;
  }

  th {
    background-color: #f8f9fc;
    border: 0px;
    color: var(--adm-color-title);
  }
}
