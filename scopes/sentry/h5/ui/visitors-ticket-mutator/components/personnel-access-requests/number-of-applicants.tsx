import { MinusOutlined, PlusOutlined } from '@ant-design/icons';
import React, { type MutableRefObject, type RefObject } from 'react';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.h5.ui.button';
import { Form } from '@manyun/base-ui.h5.ui.form';
import { Input } from '@manyun/base-ui.h5.ui.input';

const CenteredLabelFormItem = styled(Form.Item)`
  .adm-list-item-content {
    align-items: center !important;
  }
`;

const InputGroup = styled.div`
  display: flex;
  align-items: center;
  justify-content: end;
`;

const StyledButton = styled(Button)`
  width: 28px;
  height: 26px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  background-color: #f8f9fc;
  border: none;

  &:disabled {
    opacity: 0.5;
    background-color: #f8f9fc;
  }
`;

const StyledInput = styled(Input)`
  width: 40px;
  height: 28px;
  text-align: center;
  font-size: 16px;
  margin: 0 2px;
  background-color: #f8f9fc;
  &:focus {
    outline: none;
  }
  .adm-input-element {
    text-align: center;
  }
`;

/**
 * 申请人数输入组件
 * @param {Object} props 组件属性
 * @param {number} props.value 当前值
 * @param {number} props.min 最小值（默认为1）
 * @param {number} props.max 最大值（默认为999）
 * @param {Function} props.onChange 值变化时的回调函数
 */
const NumberOfApplicants = ({ value = 0, min = 1, max = 999, ...props }: any) => {
  // 处理减少按钮点击
  const handleDecrease = () => {
    if (value > min) {
      props?.onChange(value - 1);
    }
  };
  // 处理增加按钮点击
  const handleIncrease = () => {
    if (value < max) {
      props?.onChange(value + 1);
    }
  };

  // 处理输入框变化
  const handleInputChange = (val: string) => {
    if (val === '') {
      // 如果清空，设置为空字符串（可以不触发变更，或者让外部做处理）
      props?.onChange?.('');
      return;
    }
    // 转换为数字
    const numValue = parseInt(val, 10);

    // 验证输入是否为有效数字
    if (!isNaN(numValue)) {
      // 确保值在允许范围内
      const newValue = Math.min(Math.max(numValue, min), max);
      props?.onChange?.(newValue);
    }
  };

  return (
    <>
      <InputGroup>
        <StyledButton onClick={handleDecrease} disabled={value <= min}>
          <MinusOutlined />
        </StyledButton>
        <StyledInput type="number" value={value} onChange={handleInputChange} min={min} max={max} />
        <StyledButton onClick={handleIncrease} disabled={value >= max}>
          <PlusOutlined />
        </StyledButton>
      </InputGroup>
    </>
  );
};
export function FormItemNumberOfApplicants() {
  return (
    <CenteredLabelFormItem
      label="申请人数"
      name="guestNum"
      layout="horizontal"
      rules={[
        {
          required: true,
          message: `申请人数必填`,
        },
      ]}
    >
      <NumberOfApplicants />
    </CenteredLabelFormItem>
  );
}
