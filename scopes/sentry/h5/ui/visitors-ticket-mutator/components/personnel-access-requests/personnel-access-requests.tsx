import {
  ExclamationCircleFilled,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
  RightOutlined,
  WarningFilled,
} from '@ant-design/icons';
import { useNavigate } from '@tanstack/react-location';
import type { DatePickerRef } from 'antd-mobile';
import { DatePicker, Dialog, List } from 'antd-mobile';
import dayjs from 'dayjs';
import debounce from 'lodash.debounce';
import { nanoid } from 'nanoid';
import React, { type MutableRefObject, type RefObject } from 'react';
import styled from 'styled-components';

import { Alert } from '@manyun/base-ui.h5.ui.alert';
import { Block } from '@manyun/base-ui.h5.ui.block';
import { Button } from '@manyun/base-ui.h5.ui.button';
import { Card } from '@manyun/base-ui.h5.ui.card';
import type { CascaderRef } from '@manyun/base-ui.h5.ui.cascader';
import { CheckList } from '@manyun/base-ui.h5.ui.check-list';
import { Checkbox } from '@manyun/base-ui.h5.ui.checkbox';
import { Divider } from '@manyun/base-ui.h5.ui.divider';
import { Footer } from '@manyun/base-ui.h5.ui.footer';
import { Form } from '@manyun/base-ui.h5.ui.form';
import { Input } from '@manyun/base-ui.h5.ui.input';
import { Loading } from '@manyun/base-ui.h5.ui.loading';
import { McUpload } from '@manyun/base-ui.h5.ui.mc-upload';
import { Popover } from '@manyun/base-ui.h5.ui.popover';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { SearchBar } from '@manyun/base-ui.h5.ui.search-bar';
import { Selector } from '@manyun/base-ui.h5.ui.selector';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { SwipeAction } from '@manyun/base-ui.h5.ui.swipe-action';
import { Tag } from '@manyun/base-ui.h5.ui.tag';
import { TextArea } from '@manyun/base-ui.h5.ui.textarea';
import { Toast } from '@manyun/base-ui.h5.ui.toast';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import type { McUploadFile } from '@manyun/base-ui.model.mc-upload-file';
import { fetchPagedVendors } from '@manyun/crm.service.fetch-paged-vendors';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { VISITOR_ENTRY_MUTATOR_ROUTE_PATH } from '@manyun/dc-brain.h5.route.routes';
import { VISITOR_ENTRY_ROUTE_PATH, generateVisitorEntry } from '@manyun/dc-brain.h5.route.routes';
import { useStoreContext } from '@manyun/dc-brain.h5.store.store';
import { TENANT_ID_IS_YG } from '@manyun/dc-brain.h5.util.common';
import { McUploadFile as McUploadFiles } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchStoreProperties } from '@manyun/dc-brain.service.fetch-store-properties';
import { mutateStoreProperties } from '@manyun/dc-brain.service.save-store-properties';
import { UserAvatar, useUser } from '@manyun/iam.h5.ui.user';
import { UserPicker } from '@manyun/iam.h5.ui.user-picker';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
// import { UserAvatar as IamUserAvatar } from '@manyun/iam.ui.user-avatar';
import { useMetadata } from '@manyun/resource-hub.gql.client.metadata';
import { useSpaces as resourcesUseSpaces } from '@manyun/resource-hub.gql.client.resources';
import { IdcPicker } from '@manyun/resource-hub.h5.ui.idc-picker';
import { MetaTypeCascader } from '@manyun/resource-hub.h5.ui.meta-type-cascader';
import { SpaceTreePicker } from '@manyun/resource-hub.h5.ui.space-tree-picker';
import type { Metadata } from '@manyun/resource-hub.model.metadata';
import { fetchMetadataByType } from '@manyun/resource-hub.service.fetch-metadata-by-type';
import { fetchRoomList } from '@manyun/resource-hub.service.fetch-room-list';
import { useLazyVisitTicket, useVisitTicketMutation } from '@manyun/sentry.gql.client.visits';
import { VisitProtectionDateAlter } from '@manyun/sentry.h5.ui.visit-protection-date-alter';
import { VisitorCard } from '@manyun/sentry.h5.ui.visitor-card';
import { businessClassificationMappingQuery } from '@manyun/sentry.service.business-classification-mapping-query';
import { depositaryDraftEntry } from '@manyun/sentry.service.depositary-draft-entry';
import { fetchVisitTicketCustomInfos } from '@manyun/sentry.service.fetch-visit-ticket-custom-infos';
import {
  useUpdateApproveAreaMutation,
  useUpdateAssigneeConfigMutation,
  useUpdateOrderAssigneeMutation,
} from '@manyun/ticket.gql.client.tickets';
import '@manyun/ticket.gql.client.tickets';
import { fetchPagedVisitors } from '@manyun/ticket.service.fetch-paged-visitors';

import { ExistingVisitsRecordAlert } from '../existing-visits-record-alert';
import type { Visitor } from '../visitor-mutator';
import { VisitorMutator } from '../visitor-mutator';
import { FormItemNumberOfApplicants } from './number-of-applicants';
import { usePersonnelAccessFormContext } from './personnel-access-form-context';
import { FormItemReportVoucher } from './report-voucher';
import { FormItemUserSelector } from './user-select';
import { isFormChanged } from './util';

const StyledDiv = styled.div`
  width: 100%;
  overflow-y: auto;
  display: flex;
  height: 100%;
  flex-direction: column;
`;

const StyledTextArea = styled(TextArea)`
  --font-size: 14px;
  & {
    font-size: 14px;
    color: var(--adm-color-text);
  }
  .adm-text-area-element {
    height: 26px;
  }
  .adm-text-area-count {
    margin-top: 0;
    padding-top: 0;
    font-size: 14px;
    color: rgba(198, 202, 209, 1);
    line-height: 22px;
  }
  & ::placeholder {
    font-size: 14px;
    color: rgba(198, 202, 209, 1);
  }
`;
const StyledPopup = styled(Popup)`
  & .popupContext .adm-list-body .adm-list-item {
    padding-left: 16px;
    .adm-list-item-content {
      padding-right: 0;
      margin-right: 16px;
    }
  }
`;
const StyledSelector = styled(Selector)`
  & {
    display: flex;
    flex: 1;
    height: 24px;
    align-items: center;
    font-size: 12px;
    .adm-space {
      display: flex;
      flex: 1;
      flex-wrap: nowrap;
      --gap: 4px !important;
      .adm-space-item {
        display: flex;
        flex: 0 1 calc((100% - (4px * 3)) / 4);
        text-align: center;

        .adm-selector-item {
          flex: 1 1 auto;
          padding: 2px 4px;
          font-size: 12px;
          color: var(--adm-color-text);
        }
        .adm-selector-item-active,
        .adm-selector-item-multiple-active {
          border: none;
          color: var(--checked-text-color);
        }
      }
    }
  }
`;
const StyledCheckbox = styled(Checkbox)`
  & {
    height: 22px;
    margin-right: 16px;
    .adm-checkbox-icon {
      width: 16px;
      height: 16px;
    }
    .adm-checkbox-content {
      line-height: 22px;
      padding-left: 4px;
    }
  }
`;
const StyledSearchBar = styled(SearchBar)`
  & {
    margin-top: 8px;
    padding: 8px 16px;
    .adm-search-bar-input-box {
      padding-left: 16px;
      border-radius: 19px;
      background-color: rgba(248, 249, 252, 1);
      .adm-search-bar-input-box-icon {
        font-size: 14px;
        margin-bottom: 2px;
      }
      .adm-search-bar-input.adm-input .adm-input-element {
        font-size: 14px;
        --font-size: 14px;
        --placeholder-color: rgba(198, 202, 210, 1);
      }
    }
  }
`;
const alarmColor = 'rgba(255, 49, 65, 1)';
const tagColor = 'rgba(184, 28, 34, 1)';
const tagBgColor = 'rgba(184, 28, 34, 0.08)';
const warningColor = 'rgba(250, 173, 20, 1)';

export type PersonnelAccessRequestsProps = {
  //编辑模式下，业务id
  ticketNumber?: string;
  visitorsTicketRef?: MutableRefObject<{ goBack: () => void } | null>;
};
type FormValues = {
  idc: string[];
  title: string;
  purpose: string;
  allowedStartTime: string;
  allowedEndTime: string;
  visitType: { value: number[]; options: any[] };
  fileInfoList: any[];
  areaInfoList: AreaInfoType[];
};
type initTreeDataType = {
  children: initTreeDataType[] | null; // children 是另一个 Space 数组或者 null
  custom: any | null; // JSON
  isVirtual: boolean | null;
  label: string; // 标签，字符串类型
  parentValue: string; // 父级的值，字符串类型
  type: string; // 类型，字符串类型
  value: string; // 值，字符串类型
  metaSubType: string;
};
type ChildItem = {
  id: number;
  parentId: string;
  relateType: number;
  value: string;
  label: string;
  isOther?: boolean; // 可选属性
};
// 根对象类型
type RootItem = {
  label: string;
  value: string;
  relateType: number;
  children: ChildItem[]; // 子项数组
};
// Define types for the mergeApproveAndAssigneeData function
type ApproveAreaItem = {
  blockGuid: string;
  resourceNo: string;
  roomCategory?: string;
};

type OrderAssigneeItem = {
  blockGuid: string;
  assigneeList: Array<{
    id: number;
    userName: string;
  }>;
};

type GroupedDataItem = {
  blockGuid: string;
  resourceNos: string[];
  roomCategorys: Set<string>;
  assigneeList: Array<{
    id: number;
    userName: string;
  }>;
};

type MergedAreaResult = {
  key: number;
  block: string;
  areas: string[];
  areaTypes: string[];
  assigneeList: Array<{
    id: number;
    userName: string;
  }>;
  roomGuidList: string[];
};

function findNodeInTree(tree: RootItem[], value: any) {
  for (const node of tree) {
    // 判断当前节点是否匹配
    if (node.value === value) {
      return node;
    }

    // 判断子节点是否匹配（假设子节点保存在 children 数组中）
    if (node.children && node.children.length > 0) {
      const result = node.children.find((child: { value: any }) => child.value === value);
      if (result) {
        return result;
      }
    }
  }

  // 如果没有找到返回 null
  return null;
}
function transformToTree(data: any[]) {
  const tree: { relateType: any; value: any; label: any; children: never[] }[] = [];
  const map = new Map(); // 用于快速查找父级

  data.forEach(
    (item: {
      id: any;
      relateType: any;
      sourceCode: any;
      sourceName: any;
      sourceSubCode: any;
      sourceSubName: any;
      targetCode: any;
      targetSubCode: any;
      tag: string;
      sort: number;
    }) => {
      const {
        id,
        relateType,
        sourceCode,
        sourceName,
        sourceSubCode,
        sourceSubName,
        targetCode,
        targetSubCode,
        tag,
        sort,
      } = item;

      // 如果一级节点不存在，创建并添加到树
      if (!map.has(sourceCode)) {
        const parentNode = {
          relateType,
          value: sourceCode,
          label: sourceName,
          tag,
          sort,
          children: tag ? null : [],
          _children: [],
        };
        tree.push(parentNode);
        map.set(sourceCode, parentNode); // 保存引用
      }

      // 添加二级节点
      const parentNode = map.get(sourceCode);
      const childNode = {
        id, // 保留二级节点的 id
        parentId: parentNode.value, // 指向父级的 value
        relateType,
        value: sourceSubCode,
        label: sourceSubName,
        isOther: targetCode === 'other', // 判断是否为 'other'
      };
      // 特殊处理 有政府 或 商务标签  不展示二级 ,_children 用来默认携带 二级数据下的第一个
      !!!parentNode.tag && parentNode.children.push(childNode);
      parentNode._children.push(childNode);
    }
  );
  // 根据sort字段对数组进行排序
  tree.sort((a, b) => {
    // 如果sort字段存在，则按sort排序
    if (a.sort !== undefined && b.sort !== undefined) {
      return a.sort - b.sort;
    }
    // 如果只有a有sort字段，a排在前面
    if (a.sort !== undefined) {
      return -1;
    }
    // 如果只有b有sort字段，b排在前面
    if (b.sort !== undefined) {
      return 1;
    }
    // 如果都没有sort字段，保持原有顺序
    return 0;
  });
  return tree;
}
function mergeApproveAndAssigneeData(
  approveAreaData: ApproveAreaItem[],
  orderAssignee: OrderAssigneeItem[]
): MergedAreaResult[] {
  // Step 1: 整合 approveAreaData 按 blockGuid
  const groupedData: Record<string, GroupedDataItem> = approveAreaData.reduce(
    (acc: Record<string, GroupedDataItem>, curr: ApproveAreaItem) => {
      const { blockGuid, resourceNo, roomCategory } = curr;

      if (!acc[blockGuid]) {
        acc[blockGuid] = {
          blockGuid,
          resourceNos: [],
          roomCategorys: new Set<string>(), // 使用 Set 去重
          assigneeList: [],
        };
      }

      // 将 resourceNo 添加到 resourceNos 数组
      acc[blockGuid].resourceNos.push(resourceNo);

      // 将 roomCategory 添加到 Set (自动去重)
      if (roomCategory) {
        acc[blockGuid].roomCategorys.add(roomCategory);
      }

      return acc;
    },
    {}
  );

  // Step 2: 将 roomCategorys 转为数组，并且从 orderAssignee 匹配 assigneeList
  Object.keys(groupedData).forEach(blockGuid => {
    groupedData[blockGuid].roomCategorys = Array.from(groupedData[blockGuid].roomCategorys);

    // Step 3: 查找 orderAssignee 匹配的 assigneeList
    const assignee = orderAssignee.find(item => item.blockGuid === blockGuid);
    if (assignee) {
      groupedData[blockGuid].assigneeList = assignee.assigneeList;
    }
  });

  // Step 4: 转换为最终结构
  return Object.values(groupedData).map((item, index) => ({
    key: index + 1, // key 从 1 开始
    block: item.blockGuid,
    areas: item.resourceNos,
    areaTypes: item.roomCategorys,
    assigneeList: item.assigneeList.length
      ? item.assigneeList.map((assignee: { id: number; userName: string }) => ({
          id: assignee.id,
          userName: assignee.userName,
        }))
      : [],
    roomGuidList: item.resourceNos,
  }));
}
export function PersonnelAccessRequestsContent({
  ticketNumber,
  visitorsTicketRef,
}: PersonnelAccessRequestsProps) {
  const navigate = useNavigate();
  const [, { checkUserId, checkCode }] = useAuthorized();
  const [{ user }] = useStoreContext();

  // 使用 PersonnelAccessFormContext 更新业务分类数据
  const personnelAccessContext = usePersonnelAccessFormContext();

  const [form] = Form.useForm();
  const formIdRef = React.useRef<string>(`${nanoid()}_${dayjs().valueOf()}_${user.userId}`);

  const initFormValuesRef = React.useRef<any>({}); //表单初始值 需要对交互复杂数据结构对比
  const formValuesRef = React.useRef<FormValues | null>(null); //表单值
  const addBlocksRef = React.useRef<{
    reSetAreaList: () => void;
    getAreaList: () => AreaInfoType[];
    setAreaList: (v: AreaInfoType[]) => void;
    refreshAreaList: () => any[];
  } | null>(null); // <AddBlocks/> 受控方法
  const addBlockBtnRef = React.useRef<HTMLDivElement>(null); //锚点定位

  const savingTaskNo = React.useRef<undefined | string>(undefined); //保存草稿后为 编辑状态提交表单

  const [step, setStep] = React.useState(1); //步骤
  const [showAlert, setShowAlert] = React.useState({
    initRender: true,
    value: false,
  }); //粘滞top Alert
  const [visitors, setVisitors] = React.useState<Visitor[]>([]); // 人员信息列表

  const [initTreeData, setInitTreeData] = React.useState<initTreeDataType[]>([]); //初始化楼栋数据
  const [initAssigneeData, setInitAssigneeData] = React.useState<any[]>([]); //初始化楼栋下所有对接人
  const [blockPopupVisible, setBlockPopupVisible] = React.useState(false); //添加楼栋 抽屉
  const [areaAlertTips, setAreaAlertTips] = React.useState<{
    open: boolean;
    value: undefined | string;
  }>({ open: false, value: undefined });

  const [isOther, setIsOther] = React.useState(false);
  const [businessCagoteryOptions, setBusinessCagoteryOptions] = React.useState<RootItem[]>();

  const idc = Form.useWatch('idc', form) ?? formValuesRef.current?.idc;
  const allowedStartTime =
    Form.useWatch('allowedStartTime', form) ?? formValuesRef.current?.allowedStartTime;
  const allowedEndTime =
    Form.useWatch('allowedEndTime', form) ?? formValuesRef.current?.allowedEndTime;
  const watchVisitType = Form.useWatch('visitType', form);

  //获取授权时间配置
  const { data: visitConfig } = useMetadata({ variables: { type: 'VISITOR_TIME' } });
  const [getOrderAssignee, { loading: orderAssigneeLoading }] = useUpdateOrderAssigneeMutation();
  const [getApproveArea, { loading: approveAreaLoading }] = useUpdateApproveAreaMutation();
  const [getAssigneeConfig, { loading: assigneeConfigLoading }] = useUpdateAssigneeConfigMutation();

  const [mutateVisitTicket, { loading: submitLoading }] = useVisitTicketMutation({
    onCompleted(data: { mutateVisitTicket: { success: any; message: any; ticketNumber: any } }) {
      if (!data.mutateVisitTicket?.success) {
        Toast.show(data.mutateVisitTicket?.message ?? '');
        return;
      }

      Toast.show('提交成功!');
      if (data.mutateVisitTicket.ticketNumber) {
        //有申请id跳转至详情页
        navigate({
          to: generateVisitorEntry({ id: data.mutateVisitTicket.ticketNumber }),
          replace: true,
        });
      } else {
        //调整至列表页
        navigate({ to: VISITOR_ENTRY_ROUTE_PATH });
      }
      form.resetFields();
      setVisitors([]);
    },
  });

  resourcesUseSpaces({
    variables: {
      nodeTypes: ['BLOCK', 'ROOM_CATEGORY', 'ROOM_TYPE', 'ROOM'],
      includeVirtualBlocks: false,
      idc: idc?.[0],
      queryRoomProperties: true,
      onlyEnableBlock: true,
      onlyEnableRoom: true,
    },
    onCompleted(data: { spaces: React.SetStateAction<initTreeDataType[]> }) {
      setInitTreeData?.(data?.spaces);
    },
  });
  // 编辑 初始化映射form
  const [getVisitTicket] = useLazyVisitTicket({
    onCompleted: (data: {
      visitTicket: {
        visitors: any;
        assigneeRoles: any;
        assigneeList: any;
        subType: any;
        thirdType: any;
        relatedTicketType: any;
        relatedTicketNumber: any;
        title: any;
        purpose: any;
        allowedTimeRage: (string | number | Date | dayjs.Dayjs | null | undefined)[];
        idc: any;
        authorizedArea: { code: any }[];
        visitorNotifyList: any;
        bizTag: string | undefined;
        fileInfoList: any[];
      };
    }) => {
      if (!data.visitTicket) {
        return;
      }
      const initVisitors = (data.visitTicket?.visitors ?? []).map(
        ({
          type,

          identificationType,
          ICN,
          LPN,
          companyName,
          operationalAuthorization,
          ...restVsitor
        }: any) => ({
          ...restVsitor,
          id: nanoid(6),
          type: [type],
          identificationType: identificationType ? [identificationType] : undefined,
          identification: ICN ?? undefined,
          mobile: {
            preValue: restVsitor.mobileAreaCode,
            realValue: restVsitor.mobile,
          },
          LPN: LPN ?? undefined,
          companyName: companyName ?? undefined,
          operationalAuthorization: operationalAuthorization ?? undefined,
          personalGoods: restVsitor.personalGoods?.join('；'),
          position: restVsitor.position ?? undefined,
        })
      );
      //映射人员
      setVisitors(pre => initVisitors);
      // 根据 code 查找对应节点
      function findNodeByCode(tree: initTreeDataType[], code: string): initTreeDataType | null {
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i];

          // 如果找到了对应的节点
          if (node.value === code) {
            return node;
          }

          // 递归查找子节点
          if (node.children) {
            const foundNode = findNodeByCode(node.children, code);
            if (foundNode) {
              return foundNode;
            }
          }
        }

        // 如果没有找到，返回 null
        return null;
      }
      // 处理原始数据，生成 _AreaInfoType_ 格式
      function processAreaInfo(
        mappedData: any[],
        initTreeData: initTreeDataType[]
      ): AreaInfoType[] {
        // 用一个对象来按 blockGuid 分组
        const groupedData: { [key: string]: AreaInfoType } = {};
        mappedData.forEach(item => {
          // 通过 . 分割 value，获取前两部分作为 blockGuid
          const blockGuid = item.value.split('.').slice(0, 2).join('.');

          // 从 initTreeData 中找到对应的 blockLabel
          const blockInfo = initTreeData.find(i => i.value === blockGuid);

          // 如果该 blockGuid 在 groupedData 中不存在，初始化它
          if (!groupedData[blockGuid]) {
            groupedData[blockGuid] = {
              blockGuid,
              blockLabel: blockInfo?.label || '',
              roomCategoryList: [],
              assigneeList: [],
            };
          }
          groupedData[blockGuid].roomCategoryList.push(item);
        });

        const result = Object.values(groupedData).map(item => {
          const blockInfo = initTreeData.find(i => i.value === item.blockGuid);
          const newList = item.roomCategoryList.map(i => {
            if (i.type === 'ROOM') {
              const parentInfo = findNodeByCode(initTreeData, i.parentValue);
              return { ...i, category: parentInfo?.parentValue?.split('.')[2] };
            }

            if (i.type === 'ROOM_CATEGORY') {
              return { ...i, category: i.value?.split('.')[2] };
            }
            return i;
          });
          const _roomCategoryList =
            blockInfo?.children?.map(i => {
              const segments = (i.value as string).split('.');
              const _str = segments[segments.length - 1];
              return {
                label: i.label,
                type: i.type,
                value: i.value,
                category: _str.charAt(0).toUpperCase() + _str.slice(1),
                isChildren: !!(Array.isArray(i.children) && i.children.length >= 1),
                checked: newList.some(
                  roomCategory =>
                    roomCategory.category?.charAt(0).toUpperCase() +
                      roomCategory.category?.slice(1) ===
                    _str.charAt(0).toUpperCase() + _str.slice(1)
                ),
                roomList: !!(Array.isArray(i.children) && i.children.length >= 1)
                  ? newList.filter(i => i.category === _str)
                  : [],
              };
            }) || [];
          return { ...item, roomCategoryList: _roomCategoryList };
        });

        return result;
      }

      const _ = data.visitTicket.authorizedArea
        .map(i => findNodeByCode(initTreeData, i.code))
        .filter(i => i != null);
      const newAreaList = processAreaInfo(_, initTreeData) as AreaInfoType[];
      (async () => {
        const {
          data: { updateChangeOrderAssignee },
        } = await getOrderAssignee({
          variables: {
            query: {
              taskNo: data.visitTicket.ticketNumber,
            },
          },
          onCompleted(data: any) {},
        });
        const {
          data: { updateChangeApproveArea },
        } = await getApproveArea({
          variables: {
            query: {
              taskNo: data.visitTicket.ticketNumber,
            },
          },
          onCompleted(data: any) {},
        });
        const { data: approveAreaData } = updateChangeApproveArea;
        const { data: orderAssignee } = updateChangeOrderAssignee;
        const _data = mergeApproveAndAssigneeData(approveAreaData, orderAssignee);
        if (_data) {
          const _areaInfoList = newAreaList.map(item => {
            const findInfo = _data.find(i => i.block === item.blockGuid);
            if (findInfo) {
              return { ...item, assigneeList: findInfo.assigneeList };
            }
            return item;
          });
          formValuesRef.current = {
            ...formValuesRef.current,
            areaInfoList: _areaInfoList,
          };
          initFormValuesRef.current = {
            ...initFormValuesRef.current,
            areaInfoList: _areaInfoList,
          };
        }
      })();

      if (data.visitTicket.bizTag && ticketNumber) {
        setSpecialtyVisitForm({ bizTag: data.visitTicket.bizTag });
      }
      const initValues = {
        visitType:
          data.visitTicket.subType && data.visitTicket.thirdType
            ? { value: [data.visitTicket.subType, data.visitTicket.thirdType] }
            : undefined,
        relatedTicketType: data.visitTicket.relatedTicketType
          ? [data.visitTicket.relatedTicketType]
          : undefined,
        relatedTicketNumber: data.visitTicket.relatedTicketNumber ?? undefined,
        title: (data.visitTicket.title as string).replace(
          data.visitTicket.bizTag ? '参观申请单' : '人员进入申请单',
          ''
        ),
        purpose: data.visitTicket.purpose?.trim(),
        allowedStartTime: dayjs(data.visitTicket.allowedTimeRage[0]).toDate(),
        allowedEndTime: dayjs(data.visitTicket.allowedTimeRage[1]).toDate(),
        idc: [data.visitTicket.idc],
        authorizedArea: data.visitTicket.authorizedArea.length
          ? data.visitTicket.authorizedArea.map(({ code }) => code)
          : undefined,
        fileInfoList: data.visitTicket.fileInfoList,
      };
      initFormValuesRef.current = {
        ...initFormValuesRef.current,
        ...initValues,
        visitors: initVisitors,
      };
      form.setFieldsValue(initValues);
    },
  });

  // 获取 特殊人员类型数据
  const setSpecialtyVisitForm = async ({ bizTag }: { bizTag: string }) => {
    if (ticketNumber) {
      const { data: customInfos, error } = await fetchVisitTicketCustomInfos({
        ticketNumber,
        bizTag,
      });
      if (error) {
        return;
      }
      if (customInfos?.visitors && customInfos.visitors?.length >= 1) {
        const { guestNum, companyName, name, contactWay, userId } = customInfos.visitors[0];
        const _values = {
          guestNum,
          guestCompany: companyName,
          receptionName: name,
          receptionId: userId,
          receptionPhone: contactWay,
        };
        initFormValuesRef.current = {
          ...initFormValuesRef.current,
          ..._values,
        };
        form.setFieldsValue(_values);
      }
    }
  };

  // 时间选择 逻辑
  const timeLimitDays = React.useMemo(() => {
    const timeConfig = visitConfig?.metadata?.find(
      (record: { code: string }) => record.code === idc?.[0]
    );
    return timeConfig ? Number(timeConfig.name) : null;
  }, [idc, visitConfig?.metadata]);
  const subTitle = personnelAccessContext?.isSpecialtyVisitType ? '参观申请单' : '人员进入申请单';
  // 添加楼栋后 锚点定位
  const scrollToButton = () => {
    // 确保按钮存在，如果存在就滚动到按钮
    if (addBlockBtnRef.current) {
      addBlockBtnRef.current.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'start', // 滚动到视口顶部
      });
    }
  };
  // 添加人员
  const onVisitorsChange = (visitor: Visitor, operate: 'add' | 'edit') => {
    setVisitors(pre => {
      if (operate === 'add') {
        return [...pre, visitor];
      } else {
        return pre.map(item => (item.id === visitor.id ? { ...item, ...visitor } : item));
      }
    });
  };

  // 提交表单
  const onSubmit = async (mode?: string) => {
    const {
      idc,
      title,
      visitType,
      allowedStartTime,
      allowedEndTime,
      purpose = '',
      fileInfoList,
      receptionName,
      receptionId,
      receptionPhone,
      guestCompany,
      guestNum,
    } = formValuesRef.current as FormValues;
    if (mode === 'drafts') {
      if (!idc?.[0]) {
        Toast.show('请填写进入园区');
        return;
      }
      if (!title) {
        Toast.show('请填写工单标题');
        return;
      }
      if (!visitType?.value) {
        Toast.show('请填写业务分类');
        return;
      }
    }
    const idcValue = idc[0];
    const errorInfos = addBlocksRef.current?.refreshAreaList();

    if (
      errorInfos &&
      errorInfos
        .filter(i => !personnelAccessContext?.disableBlocks.includes(i.blockGuid))
        .some(info => info.errorTypes.length >= 1) &&
      mode !== 'drafts'
    ) {
      return;
    }
    const areaList = addBlocksRef.current?.getAreaList();
    if (areaList && areaList.length === 0 && mode !== 'drafts') {
      Toast.show('请至少添加一个区域');
      return;
    }

    if (mode !== 'drafts') {
      if (visitors.length === 0 && !personnelAccessContext?.isGovernments) {
        Toast.show('至少添加一个人员');
        return;
      }
      if (visitors.length >= 1 && !personnelAccessContext?.isGovernments) {
        let is = false;
        visitors.forEach((visitor, index) => {
          const key = index + 1;
          if (!visitor.type?.[0]) {
            Toast.show(`请填写第${key}组人员类型`);
            is = true;
            return;
          }
          if (!visitor.name) {
            Toast.show(`请填写第${key}组人员姓名`);
            is = true;
            return;
          }
          if (!visitor.identification) {
            Toast.show(`请填写第${key}组证件号码`);
            is = true;
            return;
          }
          if (!visitor.mobile?.realValue) {
            Toast.show(`请填写第${key}组联系方式`);
            is = true;
            return;
          }
          if (!visitor.position) {
            Toast.show(`请填写第${key}组职位`);
            is = true;
            return;
          }
          if (!visitor.companyName) {
            Toast.show(`请填写第${key}组公司名称`);
            is = true;
            return;
          }
        });
        if (is) {
          return;
        }
      }
    }
    if (!areaList && mode !== 'drafts') {
      return;
    }
    const _initTreeData = initTreeData.map(i => ({
      type: i.type,
      label: i.label,
      value: i.value,
      metaSubType: i.metaSubType,
    }));

    const roomCategoryList: string[] = [];
    const roomCategoryGuidList: string[] = [];
    const roomGuids: string[] = [
      ...(personnelAccessContext?.disableBlocks?.filter(i => i.split('.')?.[1] !== 'Z') ?? []),
    ];
    // 检查 selectedValues 中是否已经包含了一个机房类型（IDC_BLOCK）
    const hasSelectedIdcBlock = _initTreeData.some(
      block => areaList?.some(i => i.blockGuid === block.value) && block.metaSubType === 'IDC_BLOCK'
    );

    // 是否存在变电站类型（SUBSTATION）
    const hasSubstation = _initTreeData.some(
      block =>
        areaList?.some(i => i.blockGuid === block.value) && block.metaSubType === 'POWER_STATION'
    );
    areaList?.forEach(areaInfo => {
      const [idc, _block] = areaInfo.blockGuid?.split('.');
      const visitTypeValue = personnelAccessContext?.visitType?.value?.[0];
      const isInfrastructure =
        personnelAccessContext?.initDisableVisitTypes.includes(visitTypeValue);

      if (
        _block === 'Z' &&
        !personnelAccessContext?.isSpecialtyVisitType &&
        isInfrastructure &&
        (hasSelectedIdcBlock || hasSubstation)
      ) {
        return;
      }
      areaInfo.roomCategoryList.length >= 1
        ? areaInfo.roomCategoryList.forEach(i => {
            if (!i.isChildren && i.checked) {
              roomCategoryList.push(i.category);
              roomCategoryGuidList.push(i.value);
            } else if (i.checked) {
              roomGuids.push(...i.roomList.map(i => i.value));
            }
          })
        : roomGuids.push(areaInfo.blockGuid);
    });
    if (roomCategoryList.length >= 1 && roomCategoryGuidList.length >= 1) {
      const { error, data } = await fetchRoomList({
        pageNum: 1,
        pageSize: 5000,
        idcTag: idcValue,
        operationStatus: 'ON',
        // @ts-ignore
        roomCategoryList: [...new Set(roomCategoryList)],
        roomCategoryGuidList,
        isAuth: false,
      });
      if (data?.data) {
        data.data?.forEach(item => roomGuids.push(item.guid));
      }
    }
    const deEmphasizeRoomGuids = [...new Set(roomGuids)];
    console.log(
      areaList,
      { roomCategoryList, roomCategoryGuidList, roomGuids },
      'areaList-222222-提交'
    );
    const draftsInfo = personnelAccessContext?.visitTypeInfo.tag
      ? {
          bizTag: personnelAccessContext?.visitTypeInfo.tag,
          receptionName,
          receptionId,
          receptionPhone,
          guestCompany,
          guestNum,
        }
      : {};
    const param = {
      variables: {
        formId: formIdRef.current,
        ticketNumber: ticketNumber ?? savingTaskNo.current,
        allowedTimeRange: [
          dayjs(allowedStartTime).startOf('day').valueOf(),
          dayjs(allowedEndTime).endOf('day').startOf('second').valueOf(),
        ],
        idc: idcValue!.toString(),
        purpose: purpose!,
        title: `${title}${subTitle}`,
        visitType: visitType?.value?.[0].toString()!,
        visitSecondaryType: visitType?.value?.[1]
          ? visitType?.value?.[1].toString()
          : personnelAccessContext?.visitTypeInfo._children?.[0]?.value,
        authorizedArea: deEmphasizeRoomGuids,
        visitors: visitors.map(visitor => ({
          identificationType: visitor.identificationType?.[0]!.toString(),
          ICN: visitor.identification?.trim(),
          LPN: visitor.LPN,
          companyName: visitor.companyName,
          mobile: visitor.mobile?.realValue!,
          mobileAreaCode: visitor.mobile?.preValue!,
          name: visitor.name!,
          operationalAuthorization: visitor.operationalAuthorization,
          type: visitor.type?.[0]?.toString()!,
          position: visitor.position,
          personalGoods: visitor.personalGoods ? visitor.personalGoods.split('；') : undefined,
        })),
        fileInfoList: fileInfoList?.map((item: McUploadFile) => ({
          ...item,
          src: item.src,
          uploadUser: { ...item.uploadUser, __typename: undefined },
          __typename: undefined,
        })),
        blockAssigneeList: personnelAccessContext?.isGovernments
          ? []
          : areaList
              ?.map(authorizedArea => {
                // @ts-ignore
                if (authorizedArea.assigneeList.length >= 1) {
                  return {
                    blockGuid: authorizedArea.blockGuid,
                    assigneeList: authorizedArea.assigneeList,
                  };
                }
              })
              ?.filter(i => i != null),
        ...draftsInfo,
      },
    };
    if (mode === 'drafts') {
      return Promise.resolve(param.variables);
    }
    mutateVisitTicket(param);
  };
  // 保存草稿
  const savingDrafts = debounce(async () => {
    const svcQuery = await onSubmit('drafts');
    if (svcQuery) {
      const params = {
        formId: svcQuery.formId,
        // orderType: svcQuery.source === 'customers-service' ? 'VISITOR' : undefined,
        taskNo: svcQuery.ticketNumber,
        approveEndTime: svcQuery.allowedTimeRange[1],
        approveStartTime: svcQuery.allowedTimeRange[0],
        enterReason: svcQuery.purpose,
        idcTag: svcQuery.idc,
        approveArea: svcQuery.authorizedArea.join(','),
        relateTaskNo: svcQuery.relatedTicketNumber,
        relateTaskType: svcQuery.relatedTicketType,
        taskSubType: svcQuery.visitType,
        reasonType: svcQuery.visitSecondaryType,
        taskTitle: svcQuery.title,
        visitorInfos: svcQuery.visitors.map(visitor => ({
          certificateType: visitor.identificationType,
          companyName: visitor.companyName,
          phoneCountryCode: visitor.mobileAreaCode,
          contactWay: visitor.mobile,
          identityNo: visitor.ICN,
          name: visitor.name,
          operable: visitor.operationalAuthorization === 'yes',
          plateNo: visitor.LPN,
          visitorType: visitor.type,
          position: visitor.position,
          personalItem: visitor.personalGoods?.join(','),
        })),
        visitorNotifyList: svcQuery.visitorNotices?.map(notice => ({
          email: notice.email,
          userType: notice.type === 'external' ? 'OUTSIDE' : 'SYSTEM',
          userId: notice.userId,
          userName: notice.userName,
        })),
        fileInfoList: svcQuery.fileInfoList?.map(file =>
          McUploadFiles.fromJSON(file).toApiObject()
        ),
        fileIdList: svcQuery.fileIdList,
        assigneeList: svcQuery.assigneeUsers,
        assigneeRoleList: svcQuery.assigneeRoles,
        blockAssigneeList: svcQuery.blockAssigneeList,
        bizTag: svcQuery.bizTag,
        receptionId: svcQuery.receptionId,
        receptionName: svcQuery.receptionName,
        receptionPhone: svcQuery.receptionPhone,
        guestCompany: svcQuery.guestCompany,
        guestNum: svcQuery.guestNum,
      };

      const { data, error } = await depositaryDraftEntry(params);

      if (error) {
        Toast.show(error.message);
        return;
      }
      if (data) {
        savingTaskNo.current = data;
        initFormValuesRef.current = {
          ...form.getFieldsValue(),
          visitors,
          areaInfoList: addBlocksRef.current?.getAreaList(),
        };
        Toast.show('保存草稿成功');
      }
    }
  }, 300);

  React.useEffect(() => {
    // 重新提交 进行映射
    if (ticketNumber) {
      (async () => {
        await getVisitTicket({ variables: { ticketNumber, maskedIdNumber: false } });
      })();
    }
  }, [getVisitTicket, ticketNumber]);
  React.useEffect(() => {
    // 阳高 做的中联和字节映射配置逻辑 导致 需单独处理
    // relateType	number	关联类型，0:中联关联字节；1:字节关联中联
    (async () => {
      const { data, error } = await businessClassificationMappingQuery({ relateType: 0 });
      if (error) {
        console.error(error);
        return;
      }
      if (data?.data) {
        const treeData = transformToTree(data.data)?.filter(i => {
          if (i.tag === 'BUSINESS') {
            return checkCode('visittype-commercial-tag');
          }
          if (i.tag === 'GOVERNMENT') {
            return checkCode('visittype-governments-tag');
          }
          return i;
        });
        setBusinessCagoteryOptions(treeData || undefined);
        if (!ticketNumber && treeData) {
          // 业务类型设置默认值
          const firstLevelOption = treeData[0];
          if (firstLevelOption?.children && firstLevelOption?.children.length > 0) {
            const secondLevelOption = firstLevelOption.children[0];
            form.setFieldValue('visitType', {
              value: [firstLevelOption.value, secondLevelOption.value],
              options: [firstLevelOption, secondLevelOption],
            });
          }

          if (firstLevelOption?.tag && firstLevelOption?._children.length > 0) {
            const secondLevelOption = firstLevelOption?._children[0];
            form.setFieldValue('visitType', {
              value: [firstLevelOption.value, secondLevelOption.value],
              options: [firstLevelOption, secondLevelOption],
            });
          }
        }
      }
    })();
    getAssigneeConfig({
      variables: {
        query: {
          customerNo: 'SYSTEM',
        },
      },
      onCompleted(data: any) {
        const { updateChangeAssigneeConfig } = data;
        setInitAssigneeData(updateChangeAssigneeConfig.data);
      },
    });
  }, [ticketNumber]);

  React.useEffect(() => {
    // 必填项 根据 业务类型 逻辑查找
    const _visitType = watchVisitType?.value;
    if (businessCagoteryOptions && _visitType && Array.isArray(_visitType)) {
      // @ts-ignore
      setIsOther(findNodeInTree(businessCagoteryOptions, _visitType[1])?.isOther ?? false);
      personnelAccessContext?.updateData({
        visitType: watchVisitType,
        visitTypeInfo: businessCagoteryOptions.find(i => i.value === _visitType[0]),
      });
    }
  }, [watchVisitType, businessCagoteryOptions]);

  React.useEffect(() => {
    if (user.userId && areaAlertTips.value === 'NoMoreTips') {
      mutateStoreProperties({
        type: `${user.userId}-AreaAlertTips`,
        value: JSON.stringify(areaAlertTips.value),
      });
    }
  }, [areaAlertTips, user.userId]);
  React.useEffect(() => {
    (async () => {
      try {
        // if (step === 1 && !ticketNumber && user.userId) {
        //   // 获取用户操作表单数据 映射
        //   const { data } = await fetchStoreProperties({
        //     type: `${user.userId}-RequestForEntryForm`,
        //   });
        //   if (data) {
        //     const values = JSON.parse(data as string);
        //     Dialog.show({
        //       content: (
        //         <div
        //           style={{
        //             display: 'flex',
        //             justifyContent: 'center',
        //             flexDirection: 'column',
        //             alignItems: 'center',
        //           }}
        //         >
        //           <div>
        //             <Typography.Text>继续编辑草稿 ？</Typography.Text>
        //           </div>
        //           <div>
        //             <Typography.Text>您有未提交的内容，是否继续编辑</Typography.Text>
        //           </div>
        //         </div>
        //       ),
        //       closeOnAction: true,
        //       actions: [
        //         [
        //           {
        //             key: 'cancel',
        //             text: '否',
        //             style: { color: 'var(--adm-color-text)' },
        //           },
        //           {
        //             key: 'delete',
        //             text: '是',
        //             bold: true,
        //             style: { fontWeight: '400' },
        //             onClick: () => {
        //               formValuesRef.current = { ...values };
        //               setVisitors(values.visitors || []);
        //               setTimeout(() => {
        //                 form.setFieldsValue({
        //                   ...values,
        //                   allowedStartTime: dayjs(values.allowedStartTime).toDate(),
        //                   allowedEndTime: dayjs(values.allowedEndTime).toDate(),
        //                 });
        //               }, 0);
        //             },
        //           },
        //         ],
        //       ],
        //     });
        //     // formValuesRef.current = { ...values };
        //     // setVisitors(values.visitors || []);
        //     // setTimeout(() => {
        //     //   form.setFieldsValue({
        //     //     ...values,
        //     //     allowedStartTime: dayjs(values.allowedStartTime).toDate(),
        //     //     allowedEndTime: dayjs(values.allowedEndTime).toDate(),
        //     //   });
        //     // }, 0);
        //   }
        // }
        if (step === 2 && formValuesRef.current && user.userId) {
          // 获取用户 是否 不再提示信息
          const { data } = await fetchStoreProperties({ type: `${user.userId}-AreaAlertTips` });
          if (data) {
            const value = JSON.parse(data as string);
            setAreaAlertTips({ open: false, value });
          }
        }
        if (
          (step === 2 || step === -2) &&
          formValuesRef.current &&
          formValuesRef.current?.areaInfoList?.length >= 1 &&
          addBlocksRef.current
        ) {
          // 同步映射 进入区域
          addBlocksRef.current.setAreaList(formValuesRef.current.areaInfoList);
        }
      } catch (error) {
        console.log(error, 'fetchStoreProperties-error');
      }
    })();
  }, [step, user.userId, ticketNumber]);

  React.useEffect(() => {
    if (visitors.length >= 1 && showAlert.initRender) {
      setShowAlert({ initRender: false, value: true });
    }
  }, [visitors, showAlert.initRender]);

  React.useEffect(() => {
    if (!user.userId || ticketNumber) {
      return;
    }
    (async () => {
      const { data, error } = await fetchPagedVisitors({
        taskType: 'VISITOR',
        // @ts-ignore
        taskStatusList: ['5'],
        onlyByTime: true,
        orderByTimeInfo: 'DESC',
        creatorId: user.userId,
      });
      if (error) {
        return;
      }
      if (Array.isArray(data.data) && data.data.length >= 1) {
        Dialog.show({
          content: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              <div>
                <Typography.Title style={{ fontSize: '16px' }}>继续编辑草稿 ？</Typography.Title>
              </div>
              <div>
                <Typography.Text>您有未提交的内容，是否继续编辑</Typography.Text>
              </div>
            </div>
          ),
          closeOnAction: true,
          actions: [
            [
              {
                key: 'cancel',
                text: '否',
                style: { color: 'var(--adm-color-text)' },
              },
              {
                key: 'delete',
                text: '是',
                bold: true,
                style: { fontWeight: '400' },
                onClick: () => {
                  navigate({
                    to: `${VISITOR_ENTRY_MUTATOR_ROUTE_PATH}?ticketNumber=${data.data[0]?.id}`,
                  });
                },
              },
            ],
          ],
        });
      }
    })();
  }, [user.userId]);

  React.useEffect(() => {
    setTimeout(() => {
      initFormValuesRef.current = form.getFieldsValue();
    }, 500);
  }, [form]);
  React.useImperativeHandle(visitorsTicketRef, () => ({
    goBack: () => {
      // 校验已填内容 抛出文案 或 回退路由到首页
      if (
        isFormChanged(initFormValuesRef.current, {
          ...form.getFieldsValue(),
          ...formValuesRef.current,
          visitors,
          areaInfoList: addBlocksRef.current?.getAreaList(),
        })
      ) {
        Dialog.show({
          content: (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                flexDirection: 'column',
                alignItems: 'center',
              }}
            >
              <div>
                <Typography.Text>将会清空填写内容,</Typography.Text>
              </div>
              <div>
                <Typography.Text>可点击保存草稿暂存内容</Typography.Text>
              </div>
            </div>
          ),
          closeOnAction: true,
          actions: [
            [
              {
                key: 'cancel',
                text: '取消',
                style: { color: 'var(--adm-color-text)' },
              },
              {
                key: 'delete',
                text: '确定清空',
                bold: true,
                style: { fontWeight: '400' },
                onClick: () => {
                  // mutateStoreProperties({
                  //   type: `${user.userId}-RequestForEntryForm`,
                  //   value: JSON.stringify({
                  //     ...formValuesRef.current,
                  //     ...form.getFieldsValue(),
                  //     areaInfoList: addBlocksRef?.current
                  //       ? addBlocksRef?.current?.getAreaList()
                  //       : formValuesRef.current?.areaInfoList,
                  //     visitors,
                  //   }),
                  // });
                  navigate({ to: '/' });
                },
              },
            ],
          ],
        });
      } else {
        navigate({ to: '/' });
      }
    },
  }));

  return (
    <StyledDiv>
      {allowedStartTime && allowedEndTime && (step === 1 || step === -1) && (
        <VisitProtectionDateAlter
          style={{ padding: '6px 2px' }}
          startTime={dayjs(allowedStartTime).valueOf()}
          endTime={dayjs(allowedEndTime).valueOf()}
          showDate={false}
        />
      )}
      <Form
        style={{
          '--prefix-width': 'auto',
          padding: 0,
          flex: 1,
          overflowY: 'auto',
          marginBottom: 'calc(env(safe-area-inset-bottom) + 106px)',
        }}
        form={form}
        initialValues={{
          allowedStartTime: dayjs().startOf('day').toDate(),
          allowedEndTime: dayjs().endOf('day').toDate(),
        }}
      >
        {(step === 1 || step === -1) && (
          <Form.Header>
            <Typography.Title
              ellipsis
              style={{
                margin: '8px 0 4px 0',
                lineHeight: '24px',
                fontSize: '16px',
                fontWeight: 500,
              }}
            >
              <div
                style={{
                  display: 'inline-block',
                  margin: '0 9px 0 4px',
                  position: 'relative',
                  top: '-3px',
                  width: '6px',
                  height: '6px',
                  backgroundColor: 'var(--adm-color-primary)', // 圆点的颜色
                  borderRadius: '50%', // 使其变为圆形
                }}
              />
              基本信息
            </Typography.Title>
          </Form.Header>
        )}
        <div style={step === 1 || step === -1 ? {} : { display: 'none' }}>
          <Form.Item
            label="业务分类"
            name="visitType"
            layout="vertical"
            trigger="onConfirm"
            rules={[
              {
                required: true,
                message: `业务分类必选`,
              },
              {
                validator: (_: any, val: { value: string | any[]; options: any }) => {
                  if (val?.options?.length === 2) {
                    if (
                      val &&
                      val.value &&
                      Array.isArray(val.value) &&
                      val.value.length > 0 &&
                      val.value.length !== 2
                    ) {
                      return Promise.reject('业务分类需要到二级子类型');
                    }
                  }

                  if (val && val.value && Array.isArray(val.value) && val.value.length === 0) {
                    return Promise.reject('业务分类必选');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            getValueFromEvent={(value: any, options: any) => {
              return {
                value,
                options,
              };
            }}
            getValueProps={(value: { value: any }) => {
              return { value: value?.value ? value?.value : value };
            }}
            onClick={(_: any, ref: RefObject<CascaderRef>) => {
              ref.current?.open();
            }}
          >
            <MetaTypeCascader
              title={
                <Typography.Title style={{ fontSize: '16px', lineHeight: '24px', fontWeight: 500 }}>
                  业务分类
                </Typography.Title>
              }
              metaType={['VISITOR', 'VISITOR_THIRD_CATEGORY']}
              treeData={businessCagoteryOptions}
              onCompleted={() => {
                if (!ticketNumber && !TENANT_ID_IS_YG) {
                  form.setFieldValue('visitType', { value: ['visitor', 'visitor'] });
                }
              }}
            >
              {items =>
                items && items.length > 0 ? (
                  <Typography.Text style={{ fontSize: '14px' }}>
                    {items.map(item => item?.label).join('-')}
                  </Typography.Text>
                ) : (
                  <Input placeholder="请选择" readOnly />
                )
              }
            </MetaTypeCascader>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(pre: FormValues, next: FormValues) => {
              return pre.idc !== next.idc || pre.title !== next.title;
            }}
          >
            {({ getFieldValue }: { getFieldValue: (name: string) => any }) => {
              const inpValue = getFieldValue('title');
              return (
                <Form.Item
                  label="工单标题"
                  name="title"
                  rules={[
                    {
                      required: true,
                      message: `工单标题必填`,
                    },
                    {
                      whitespace: true,
                      validator: (_: any, val: any) => {
                        if ((val || '').length < 21) {
                          return Promise.resolve();
                        }
                        return Promise.reject('最多输入 20 个字符');
                      },
                    },
                  ]}
                >
                  <TitleInput
                    placeholder={
                      personnelAccessContext?.isSpecialtyVisitType
                        ? '请输入来访人员单位简称'
                        : '请输入公司简称'
                    }
                    inpValue={inpValue}
                    subTitle={subTitle}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item
            name="allowedStartTime"
            label="开始时间"
            dependencies={['idc']}
            rules={[
              {
                required: true,
                validator: (_: any, val: any) => {
                  if (!val) {
                    return Promise.reject('开始时间必选');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            trigger="onConfirm"
            onClick={(_: any, ref: RefObject<DatePickerRef>) => {
              ref.current?.open();
            }}
          >
            {/* @ts-ignore */}
            <DatePicker precision="day" min={new Date()}>
              {value =>
                !value ? (
                  <Input placeholder="请选择" readOnly />
                ) : (
                  <>
                    <Typography.Text style={{ fontSize: '14px' }}>
                      {dayjs(value).format('YYYY-MM-DD')}
                    </Typography.Text>
                    <Typography.Text style={{ marginLeft: '4px', fontSize: '14px' }}>
                      00:00
                    </Typography.Text>
                  </>
                )
              }
            </DatePicker>
          </Form.Item>
          <Form.Item
            name="allowedEndTime"
            label="结束时间"
            dependencies={['allowedStartTime']}
            rules={[
              {
                required: true,
                validator: (
                  _: any,
                  val: string | number | Date | dayjs.Dayjs | null | undefined
                ) => {
                  if (!val) {
                    return Promise.reject(`结束时间必选`);
                  }
                  const startTime = form.getFieldValue('allowedStartTime');

                  if (
                    (val && dayjs().endOf('day').diff(dayjs(val).endOf('day')) > 0) ||
                    (startTime && dayjs().endOf('day').diff(dayjs(startTime).endOf('day')) > 0)
                  ) {
                    return Promise.reject(`申请需在更合理的时间区间`);
                  }
                  if (
                    startTime &&
                    val &&
                    dayjs(startTime).startOf('day').diff(dayjs(val).endOf('day')) >= 0
                  ) {
                    return Promise.reject('结束时间需大于开始时间');
                  }
                  if (
                    timeLimitDays &&
                    startTime &&
                    val &&
                    dayjs(val).add(1, 'day').diff(dayjs(startTime), 'day') > timeLimitDays
                  ) {
                    return Promise.reject(`开始时间与结束时间不可超过${timeLimitDays}天`);
                  }
                  if (dayjs(val).isAfter(dayjs().add(13, 'day'))) {
                    return Promise.reject(`申请时间不可申请逾今14天`);
                  }
                  return Promise.resolve();
                },
              },
            ]}
            trigger="onConfirm"
            onClick={(_: any, ref: RefObject<DatePickerRef>) => {
              ref.current?.open();
            }}
          >
            {/* @ts-ignore */}
            <DatePicker precision="day" min={new Date()}>
              {value =>
                !value ? (
                  <Input placeholder="请选择" readOnly />
                ) : (
                  <>
                    <Typography.Text style={{ fontSize: '14px' }}>
                      {dayjs(value).format('YYYY-MM-DD')}
                    </Typography.Text>
                    <Typography.Text style={{ marginLeft: '4px', fontSize: '14px' }}>
                      23:59
                    </Typography.Text>
                  </>
                )
              }
            </DatePicker>
          </Form.Item>
          <Form.Item
            label="进入园区"
            name="idc"
            rules={[
              {
                required: true,
                message: `进入园区必选`,
              },
            ]}
            trigger="onConfirm"
            layout="vertical"
            onClick={(_: any, ref: RefObject<DatePickerRef>) => {
              ref.current?.open();
            }}
          >
            <IdcPicker
              title="进入园区"
              authorizedOnly={!TENANT_ID_IS_YG}
              onConfirm={() => {
                setVisitors([]);
                formValuesRef.current = { ...formValuesRef.current, areaInfoList: [] };
              }}
            />
          </Form.Item>
          {/* 特殊业务分类 类型 业务逻辑区分*/}
          {!personnelAccessContext?.isSpecialtyVisitType && (
            <>
              <Form.Item
                label="申请原因"
                name="purpose"
                rules={
                  watchVisitType?.value?.[0] === 'other' || isOther
                    ? [
                        {
                          required: true,
                          message: `申请原因必填`,
                        },
                      ]
                    : []
                }
              >
                <StyledTextArea autoSize placeholder="请输入" maxLength={100} showCount />
              </Form.Item>
              <Form.Item
                name="fileInfoList"
                label={
                  <Typography.Title
                    style={{
                      marginBottom: '8px',
                      fontSize: '16px',
                      lineHeight: '24px',
                      fontWeight: 400,
                    }}
                  >
                    附件
                  </Typography.Title>
                }
              >
                <McUpload
                  accept=".jpg,.jpeg,.png,.doc, .docx, .xls, .xlsx"
                  maxCount={10}
                  maxFileSize={1000}
                />
              </Form.Item>
            </>
          )}
          {personnelAccessContext?.isSpecialtyVisitType && (
            <>
              <Form.Item
                label="单位名称"
                name="guestCompany"
                rules={[
                  {
                    required: true,
                    message: `单位名称必填`,
                  },
                ]}
              >
                <StyledTextArea autoSize placeholder="请输入" maxLength={50} showCount />
              </Form.Item>
              <FormItemNumberOfApplicants />
              <FormItemUserSelector form={form} />
              <Form.Item
                label="接待人联系方式"
                name="receptionPhone"
                rules={[
                  {
                    required: true,
                    validator: (_, value) => {
                      const reg = /^1[3456789]\d{9}$/;
                      if (!value) {
                        return Promise.reject(new Error('接待人联系方式必填'));
                      } else if (!reg.test(value)) {
                        return Promise.reject(new Error('联系方式格式不正确'));
                      } else if (value.length > 11) {
                        return Promise.reject(new Error('最多输入 11 个字符'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input placeholder="请填写手机号码" />
              </Form.Item>
              <FormItemReportVoucher />
              <Form.Item
                name="purpose"
                rules={[
                  {
                    required: true,
                    message: `请添加客户前置审批链接`,
                  },
                ]}
              >
                <StyledTextArea
                  autoSize
                  placeholder="添加客户前置审批链接"
                  maxLength={100}
                  showCount
                />
              </Form.Item>
            </>
          )}
        </div>
        {(step === 2 || step === -2) && (
          <>
            {showAlert.value && (
              <Alert
                style={{
                  width: '100%',
                  height: '40px',
                  position: 'fixed',
                  top: '44px',
                  padding: '4px 16px',
                  zIndex: '999',
                }}
                closableStyle={{ position: 'absolute', top: '13px', right: '16px' }}
                description={
                  <Typography.Text style={{ color: 'var(--adm-color-title)' }}>
                    左滑卡片可进行更多操作
                  </Typography.Text>
                }
                type="warning"
                closable
                icon={
                  <ExclamationCircleFilled
                    style={{ position: 'relative', top: '-1px', fontSize: '14px' }}
                  />
                }
                onClose={() => {
                  setShowAlert({ initRender: false, value: false });
                }}
              />
            )}
            <Form.Header>
              <Typography.Title
                ellipsis
                style={{
                  margin: '8px 0 4px 0',
                  lineHeight: '24px',
                  fontSize: '16px',
                  fontWeight: 500,
                  marginTop: showAlert.value ? '38px' : '8px',
                }}
              >
                <div
                  style={{
                    display: 'inline-block',
                    margin: '0 9px 0 4px',
                    position: 'relative',
                    top: '-3px',
                    width: '6px',
                    height: '6px',
                    backgroundColor: 'var(--adm-color-primary)', // 圆点的颜色
                    borderRadius: '50%', // 使其变为圆形
                  }}
                />
                进入区域
                <Typography.Text
                  style={{
                    marginLeft: '8px',
                    fontSize: '16px',
                    color: 'rgba(134, 144, 156, 1)',
                    fontWeight: 400,
                  }}
                >
                  (必填)
                </Typography.Text>
              </Typography.Title>
            </Form.Header>
          </>
        )}
        <div style={step === 2 || step === -2 ? {} : { display: 'none' }}>
          <Block style={{ padding: 0, margin: 0, position: 'relative' }}>
            <div style={{ position: 'relative' }}>
              {/* 添加楼栋 */}
              <AddBlocks
                idc={idc?.[0]}
                addBlocksRef={addBlocksRef}
                initTreeData={initTreeData}
                initAssigneeData={initAssigneeData}
                areaAlertTips={areaAlertTips}
                blockPopupVisible={blockPopupVisible}
                setBlockPopupVisible={setBlockPopupVisible}
                setAreaAlertTips={setAreaAlertTips}
                scrollToButton={scrollToButton}
                setShowAlert={setShowAlert}
              />

              <Block ref={addBlockBtnRef} style={{ padding: 0, margin: 0 }}>
                <Button
                  style={{ width: '100%', height: '40px', fontSize: '14px' }}
                  fill="none"
                  color="primary"
                  onClick={() => {
                    setBlockPopupVisible(true);
                  }}
                >
                  + 添加楼栋
                </Button>
              </Block>
            </div>
          </Block>
        </div>

        {(step === 2 || step === -2) && (
          <Form.Header>
            <Typography.Title
              ellipsis
              style={{
                margin: '8px 0 4px 0',
                lineHeight: '24px',
                fontSize: '16px',
                fontWeight: 500,
              }}
            >
              <div
                style={{
                  display: 'inline-block',
                  margin: '0 9px 0 4px',
                  position: 'relative',
                  top: '-3px',
                  width: '6px',
                  height: '6px',
                  backgroundColor: 'var(--adm-color-primary)',
                  borderRadius: '50%',
                }}
              />
              人员信息
              {!personnelAccessContext?.isGovernments && (
                <Typography.Text
                  style={{
                    marginLeft: '8px',
                    fontSize: '16px',
                    color: 'rgba(134, 144, 156, 1)',
                    fontWeight: 400,
                  }}
                >
                  (必填)
                </Typography.Text>
              )}
            </Typography.Title>
          </Form.Header>
        )}
        <div style={step === 2 || step === -2 ? {} : { display: 'none' }}>
          <Space style={{ '--gap': '0px', width: '100%' }} direction="vertical">
            {visitors.map(visitor => (
              <SwipeAction
                key={visitor.id}
                rightActions={[
                  {
                    key: 'edit',
                    text: (
                      <VisitorMutator
                        style={{ width: '100%', height: '100%' }}
                        key="edit"
                        addedVisitors={visitors.filter(item => item.id !== visitor.id)}
                        idc={idc ? (idc?.[0] as string) : undefined}
                        allowedTimeRange={
                          allowedStartTime && allowedEndTime
                            ? [dayjs(allowedStartTime).valueOf(), dayjs(allowedEndTime).valueOf()]
                            : undefined
                        }
                        visitor={visitor}
                        onOk={onVisitorsChange}
                      >
                        <Button
                          style={{
                            width: '100%',
                            height: '100%',
                            color: 'var(--adm-color-text-light-solid)',
                          }}
                          key="delete"
                          fill="none"
                          color="primary"
                          block
                        >
                          编辑
                        </Button>
                      </VisitorMutator>
                    ),
                    color: 'light',
                    onClick: () => {},
                  },
                  {
                    key: 'delete',
                    text: '删除',
                    color: 'danger',
                    onClick: e => {
                      setVisitors(pre => pre.filter(item => item.id !== visitor.id));
                    },
                  },
                ]}
                style={{ background: 'inherit' }}
              >
                <VisitorMutator
                  style={{ width: '100%', height: '100%' }}
                  key="edit"
                  addedVisitors={visitors.filter(item => item.id !== visitor.id)}
                  idc={idc ? (idc?.[0] as string) : undefined}
                  // authorizedAreas={authorizedAreas}
                  allowedTimeRange={
                    allowedStartTime && allowedEndTime
                      ? [dayjs(allowedStartTime).valueOf(), dayjs(allowedEndTime).valueOf()]
                      : undefined
                  }
                  visitor={visitor}
                  onOk={onVisitorsChange}
                >
                  <VisitorCard
                    key={visitor.id}
                    mode="create"
                    visitor={{
                      ...visitor,
                      personalGoods: (visitor.personalGoods ?? '').split('；'),
                      name: visitor.name!,
                      identificationType: visitor.identificationType
                        ? (visitor.identificationType as unknown as string[])[0]
                        : undefined,
                      mobile: visitor.mobile?.realValue!,
                      mobileAreaCode: visitor.mobile?.preValue!,
                      type: visitor.type ? (visitor.type as unknown as string[])[0] : undefined,
                    }}
                    alert={
                      <ExistingVisitsRecordAlert
                        rowKey={visitor.id}
                        wrap
                        // authorizedAreas={authorizedAreas}
                        params={{
                          idc: idc?.[0] as string,
                          // authorizedAreas: authorizedAreas,
                          allowedTimeRange:
                            allowedStartTime && allowedEndTime
                              ? [dayjs(allowedStartTime).valueOf(), dayjs(allowedEndTime).valueOf()]
                              : undefined,
                          name: visitor.name,
                          mobile: visitor?.mobile?.realValue,
                          ICN: visitor.identification,
                        }}
                      />
                    }
                    // options={businessCagoteryOptions}
                  />
                </VisitorMutator>
                <Divider
                  style={{
                    padding: '0 16px',
                    borderTop: '0px solid rgb(242, 246, 251)',
                    margin: '0',
                  }}
                />
              </SwipeAction>
            ))}
            <VisitorMutator
              addedVisitors={visitors}
              idc={idc ? (idc?.[0] as string) : undefined}
              allowedTimeRange={
                allowedStartTime && allowedEndTime
                  ? [dayjs(allowedStartTime).valueOf(), dayjs(allowedEndTime).valueOf()]
                  : undefined
              }
              onOk={onVisitorsChange}
            >
              <Block style={{ padding: 0, margin: 0 }}>
                <Button
                  style={{ width: '100%', height: '40px', fontSize: '14px' }}
                  fill="none"
                  color="primary"
                >
                  + 添加人员
                </Button>
              </Block>
            </VisitorMutator>
          </Space>
        </div>
        {/* <Form.Header>
              <Typography.Title
                ellipsis
                style={{
                  margin: '8px 0 4px 0',
                  lineHeight: '24px',
                  fontSize: '16px',
                  fontWeight: 500,
                }}
              >
                <div
                  style={{
                    display: 'inline-block',
                    margin: '0 9px 0 4px',
                    position: 'relative',
                    top: '-3px',
                    width: '6px',
                    height: '6px',
                    backgroundColor: 'var(--adm-color-primary)',
                    borderRadius: '50%',
                  }}
                />
                车辆信息
              </Typography.Title>
            </Form.Header>
            <Block style={{ padding: 0, margin: 0, position: 'relative' }}>
              <Button
                style={{ width: '100%', height: '40px', fontSize: '14px' }}
                fill="none"
                color="primary"
              >
                + 添加车辆
              </Button>
            </Block> */}
      </Form>
      <Footer style={{ height: '100px', padding: '16px' }} safeArea>
        {(step === 1 || step === -1) && (
          <div style={{ display: 'flex' }}>
            <Button
              style={{ flex: 1, height: '40px', marginRight: '12px', fontSize: '16px' }}
              fill="outline"
              color="primary"
              onClick={() => {
                formValuesRef.current = { ...formValuesRef.current, ...form.getFieldsValue() };
                savingDrafts();
              }}
            >
              保存草稿
            </Button>
            <Button
              style={{ flex: 1, height: '40px', fontSize: '16px' }}
              block
              color="primary"
              size="large"
              onClick={() => {
                form
                  .validateFields()
                  .then(async value => {
                    formValuesRef.current = { ...formValuesRef.current, ...value };
                    setStep(step === 1 ? 2 : -2);
                  })
                  .catch(err => {
                    console.log(err, 'form-0-error');
                  });
              }}
            >
              下一步
            </Button>
          </div>
        )}
        {(step === 2 || step === -2) && (
          <div style={{ display: 'flex' }}>
            <Button
              style={{ flex: 1, height: '40px', marginRight: '12px', fontSize: '16px' }}
              fill="outline"
              color="primary"
              onClick={() => {
                if (formValuesRef.current && addBlocksRef.current) {
                  formValuesRef.current = {
                    ...formValuesRef.current,
                    areaInfoList: addBlocksRef.current.getAreaList(),
                  };
                }
                setStep(-1);
              }}
            >
              上一步
            </Button>
            <Button
              style={{ flex: 1, height: '40px', marginRight: '12px', fontSize: '16px' }}
              fill="outline"
              color="primary"
              onClick={savingDrafts}
            >
              保存草稿
            </Button>
            <Button
              style={{ flex: 1, height: '40px', fontSize: '16px' }}
              block
              color="primary"
              size="large"
              loading={submitLoading}
              disabled={submitLoading}
              onClick={onSubmit}
            >
              提交
            </Button>
          </div>
        )}
      </Footer>
    </StyledDiv>
  );
}
function TitleInput({
  inpValue,
  subTitle,
  ...rest
}: {
  inpValue: string;
  subTitle: string;
  placeholder?: string;
}) {
  return (
    <>
      <Input placeholder="请输入公司简称" maxLength={13} clearable {...rest} />
      <Typography.Text type="weak" style={{ fontSize: '12px' }}>
        {inpValue}
        {subTitle}
      </Typography.Text>
    </>
  );
}
// function TitleInput({
//   inpValue,
//   subTitle,
//   ...rest
// }: {
//   inpValue: string;
//   subTitle: string;
//   placeholder?: string;
// }) {
//   const ref = React.useRef<HTMLDivElement>(null);
//   const [visible, setVisible] = React.useState(false);
//   const [suggestions, setSuggestions] = React.useState<any[]>([]);
//   const [loading, setLoading] = React.useState(false);

//   const internalOnSearch = React.useCallback(async (keyword: string) => {
//     if (!keyword || keyword.trim().length === 0) {
//       setSuggestions([]);
//       setVisible(false);
//       return;
//     }

//     setLoading(true);
//     try {
//       const { data } = await fetchPagedVendors({
//         pageNum: 1,
//         pageSize: 5,
//         vendorName: keyword,
//       });
//       if (data && data.data && data.data.length > 0) {
//         setSuggestions(data.data);
//         setVisible(true);
//       } else {
//         setSuggestions([]);
//         setVisible(false);
//       }
//     } catch (error) {
//       console.error('Error fetching suggestions:', error);
//       setSuggestions([]);
//       setVisible(false);
//     } finally {
//       setLoading(false);
//     }
//   }, []);

//   const handleSearch = React.useMemo(() => debounce(internalOnSearch, 300), [internalOnSearch]);

//   const handleSelectSuggestion = (suggestion: any) => {
//     // Assuming the company name is in vendorName field
//     const companyName = suggestion.vendorName || suggestion.name || '';
//     // Here you would update the form value
//     if (rest.onChange) {
//       rest.onChange(companyName);
//     }
//     setVisible(false);
//   };

//   return (
//     <>
//       <Popover
//         visible={visible}
//         getContainer={() => ref.current as HTMLElement}
//         content={
//           loading ? (
//             <div style={{ minWidth: '60vw' }}>
//               <Loading />
//             </div>
//           ) : (
//             <Space
//               style={{
//                 '--gap-vertical': '16px',
//                 padding: '8px 0',
//                 maxHeight: 200,
//                 overflowY: 'auto',
//                 minWidth: '60vw',
//               }}
//               direction="vertical"
//             >
//               {suggestions.map((suggestion, index) => (
//                 <Typography.Text
//                   key={index}
//                   style={{ fontSize: 'var(--adm-font-size-6)', color: 'var(--adm-color-text)' }}
//                   onClick={() => handleSelectSuggestion(suggestion)}
//                 >
//                   {suggestion.vendorName || suggestion.name || ''}
//                 </Typography.Text>
//               ))}
//             </Space>
//           )
//         }
//         placement="bottom-start"
//         onVisibleChange={visible => setVisible(visible)}
//       >
//         <div ref={ref}>
//           <Input
//             maxLength={13}
//             clearable
//             {...rest}
//             onChange={val => {
//               if (rest.onChange) {
//                 rest.onChange(val);
//               }
//               handleSearch(val);
//             }}
//             onBlur={() => {
//               setTimeout(() => {
//                 setVisible(false);
//               }, 200);
//             }}
//           />
//         </div>
//       </Popover>
//       <Typography.Text type="weak" style={{ fontSize: '12px' }}>
//         {inpValue}
//         {subTitle}
//       </Typography.Text>
//     </>
//   );
// }

type AreaInfoType = {
  blockGuid: string;
  blockLabel: string;
  roomCategoryList: {
    isChildren: boolean;
    type: string;
    value: string;
    label: string;
    category: string;
    checked: boolean;
    roomList: any[];
  }[];
  assigneeList: { id: number; userName: string }[] | [] | undefined;
  areaError?: boolean;
  assigneeError?: boolean;
};
type RoomTreePickerRefType = {
  open: () => void;
  close: () => void;
};
function AddBlocks({
  idc,
  addBlocksRef,
  blockPopupVisible,
  initTreeData,
  initAssigneeData,
  areaAlertTips,
  setBlockPopupVisible,
  setAreaAlertTips,
  scrollToButton,
  setShowAlert,
}: {
  idc: string;
  addBlocksRef: any;
  blockPopupVisible: boolean;
  initTreeData: initTreeDataType[];
  initAssigneeData: any[];
  areaAlertTips: { open: boolean };
  setBlockPopupVisible: (v: boolean) => void;
  setAreaAlertTips: (v: any) => void;
  scrollToButton: () => void;
  setShowAlert: (v: any) => void;
}) {
  const personnelAccessContext = usePersonnelAccessFormContext();

  const blockCheckListRef = React.useRef<{
    onResetBlocks: () => void;
    onDeleteBlock: ({ blockGuid, editStatus }: { blockGuid: string; editStatus?: boolean }) => void;
    onRecognize: () => string[];
  } | null>(null);
  const roomTreePickerRef = React.useRef<RoomTreePickerRefType | null>(null);
  const initAlertTipsRenderRef = React.useRef(1);

  const [areaInfoList, setAreaInfoList] = React.useState<AreaInfoType[]>([
    // {
    //   blockGuid: undefined,
    //   roomCategoryList: [],
    //   assigneeList: [],
    //   roomList:[]
    //   // approveArea	string	授权区域，逗号隔开
    //   //         blockAssigneeList: yg_authorizedAreaList
    //   //           .map(authorizedArea => {
    //   //             if (authorizedArea.assigneeList.length >= 1) {
    //   //               return {
    //   //                 blockGuid: authorizedArea.block,
    //   //                 assigneeList: authorizedArea.assigneeList,
    //   //               };
    //   //             }
    //   //           })
    //   //           ?.filter(i => i != null),
    // },
  ]);

  // 单独处理特殊区域包间
  const [roomTreePickerStatuses, setRoomTreePickerStatuses] = React.useState<{
    key: string | undefined;
    blockGuid: string | undefined;
    blockLabel: string | undefined;
    categoryLabel: string | undefined;
    category: string | undefined;
  }>({
    key: undefined,
    blockGuid: undefined,
    blockLabel: undefined,
    categoryLabel: undefined,
    category: undefined,
  });

  // 对接人
  const [assigneeInfo, setAssigneeInfo] = React.useState<{
    key?: string | undefined;
    visible: boolean;
    blockGuid: string | undefined;
  }>({
    key: undefined,
    visible: false,
    blockGuid: undefined,
  });
  // 已选对接人 详情
  const [assigneeDetailVisible, setAssigneeDetailVisible] = React.useState<{
    key: string;
    value: boolean;
    assigneeList: [] | { id: number; userName: string }[] | undefined;
  }>({
    key: '',
    value: false,
    assigneeList: [],
  });
  // 区域全选默认值受控
  const [checkValueList, setCheckValueList] = React.useState<
    { category: string | boolean; check: boolean }[]
  >([]);
  const checkValueListRef = React.useRef(checkValueList);

  const onAddBlocks = React.useCallback(() => {
    const _addBlocks = blockCheckListRef.current
      ?.onRecognize()
      .map(blockGuid => {
        const areaInfo = initTreeData?.find(i => i.value === blockGuid);
        // initAssigneeData assigneeType === 0自动确认类型 userType===1 为默认对接人 映射逻辑
        const assigneeInfo = initAssigneeData?.find(
          i =>
            i.blockGuid === blockGuid &&
            i.assigneeType === 1 &&
            !personnelAccessContext?.isGovernments
        );
        const initAssigneeList = assigneeInfo?.assigneeList
          ?.filter(i => i.userType === 1)
          .map(i => ({ id: i.id, userName: i.userName }));
        if (areaInfo) {
          return {
            blockGuid,
            blockLabel: areaInfo.label,
            roomCategoryList:
              areaInfo.children?.map(i => {
                const segments = (i.value as string).split('.');
                const _str = segments[segments.length - 1];
                return {
                  label: i.label,
                  type: i.type,
                  value: i.value,
                  category: _str.charAt(0).toUpperCase() + _str.slice(1),
                  checked: false,
                  isChildren: !!(Array.isArray(i.children) && i.children.length >= 1),
                  roomList: [],
                };
              }) || [],
            assigneeList: initAssigneeList || [],
          };
        }
      })
      .filter(i => i != null);
    if (_addBlocks?.length === 0) {
      setAreaInfoList([]);
    }
    if (_addBlocks && _addBlocks?.length >= 1) {
      const updateBlocks = _addBlocks.map(blockInfo => {
        const _preBlocks = areaInfoList.find(
          i =>
            i.blockGuid === blockInfo.blockGuid &&
            ((i.assigneeList && i.assigneeList?.length >= 1) ||
              i.roomCategoryList.some(i => i.checked)) // i.roomList.length >= 1
        );
        if (_preBlocks) {
          return _preBlocks;
        }
        return blockInfo;
      });
      setAreaInfoList(updateBlocks);
    }

    if (_addBlocks && _addBlocks.length >= 1 && !!initAlertTipsRenderRef.current) {
      initAlertTipsRenderRef.current = initAlertTipsRenderRef.current - 1;
      setAreaAlertTips((preState: any) => {
        if (preState.value === 'NoMoreTips' || preState.value === 'No') {
          return preState;
        }
        return { open: true, value: undefined };
      });
      setShowAlert((preState: any) => {
        if (!preState.initRender) {
          return preState;
        }
        return { initRender: false, value: true };
      });
    }
    setBlockPopupVisible(false);
    scrollToButton();
  }, [initTreeData, initAssigneeData, areaInfoList]);

  const onDelete = React.useCallback((areaInfo: AreaInfoType) => {
    blockCheckListRef.current?.onDeleteBlock({ blockGuid: areaInfo.blockGuid });
    setAreaInfoList(preState => preState.filter(area => area.blockGuid !== areaInfo.blockGuid));
    setAssigneeInfo({ key: undefined, visible: false, blockGuid: undefined });
  }, []);
  const translate = (areaCheckList: AreaInfoType[]) => {
    return areaCheckList.map((area: AreaInfoType) => {
      const initInfo = initAssigneeData.find(
        i =>
          i.blockGuid === area.blockGuid &&
          i.assigneeType === 1 &&
          !personnelAccessContext?.isGovernments
      );
      let assigneeError = false;
      let areaError = false;
      const errorInfo = {
        blockGuid: area.blockGuid,
        errorTypes: [] as string[],
        errorValues: [] as any[],
      };

      if (initInfo && initInfo.assigneeList.length >= 1 && area.assigneeList?.length === 0) {
        assigneeError = true;
        errorInfo.errorTypes.push('assigneeError');
      }

      if (area.roomCategoryList.length >= 1) {
        areaError = !area.roomCategoryList.some(i => i.checked);
        if (areaError) {
          errorInfo.errorTypes.push('areaError');
        }
      }
      return { ...area, assigneeError, areaError };
    });
  };
  // 处理单独点击选中项变化
  const onCheckCategory = React.useCallback(
    (checks: string[], checkInfo: AreaInfoType) => {
      const areaCheckList = areaInfoList.map(area => {
        if (area.blockGuid === checkInfo.blockGuid) {
          // 通过检查前后勾选的变化，找出勾选或取消的项目
          const previousValue = area.roomCategoryList
            .filter(categoryInfo => categoryInfo.checked)
            .map(categoryInfo => categoryInfo.category);
          const addedItems = checks.filter(item => !previousValue.includes(item));
          const removedItems = previousValue.filter(item => !checks.includes(item));

          const updatedRoomCategoryList = area.roomCategoryList.map(categoryInfo => {
            // 特殊 IT区域 点击勾选
            if (
              (addedItems.includes(categoryInfo.category) ||
                removedItems.includes(categoryInfo.category)) &&
              categoryInfo.isChildren
            ) {
              roomTreePickerRef.current?.open();
              setRoomTreePickerStatuses({
                ...roomTreePickerStatuses,
                blockGuid: area.blockGuid,
                blockLabel: area.blockLabel,
                categoryLabel: categoryInfo.label,
                category: categoryInfo.category,
              });
            }

            // 其余正常checked逻辑
            if (
              (addedItems.includes(categoryInfo.category) ||
                removedItems.includes(categoryInfo.category)) &&
              !categoryInfo.isChildren
            ) {
              return {
                ...categoryInfo,
                checked: !categoryInfo.isChildren && checks.includes(categoryInfo.category),
              };
            }
            return categoryInfo;
          });
          return {
            ...area,
            roomCategoryList: updatedRoomCategoryList,
          };
        }
        return {
          ...area,
        };
      });
      // 校验
      const updatedAreaInfoList = translate(areaCheckList);
      setAreaInfoList(updatedAreaInfoList);
    },
    [areaInfoList]
  );
  // 全选列触发逻辑
  const onAllCheckCategory = React.useCallback(
    (checkInfo: { check: boolean; category: string }) => {
      const areaCheckList = areaInfoList.map((area: AreaInfoType) => {
        return {
          ...area,
          roomCategoryList: area.roomCategoryList.map(i => {
            if (i.category === checkInfo.category && !i.isChildren) {
              return { ...i, checked: checkInfo.check };
            }
            return i;
          }),
        };
      });
      // 校验
      const updatedAreaInfoList = translate(areaCheckList);
      setAreaInfoList(updatedAreaInfoList);
    },
    [areaInfoList]
  );
  // 初始化当前园区下所有楼栋
  const memoInitBlocks = React.useMemo(
    () =>
      initTreeData.map(i => ({
        type: i.type,
        label: i.label,
        value: i.value,
        metaSubType: i.metaSubType,
      })),
    [initTreeData]
  );
  const memoCategoryList = React.useMemo(
    () => [
      ...new Set([
        ...(areaInfoList
          ?.map(areaInfo => areaInfo.roomCategoryList.map(i => !i.isChildren && i.category))
          .flat() || []),
      ]),
    ],
    [areaInfoList]
  );

  React.useImperativeHandle(addBlocksRef, () => ({
    reSetAreaList: () => {
      setAreaInfoList([]);
    },
    getAreaList: () => {
      return areaInfoList;
    },
    setAreaList: (list: AreaInfoType[]) => {
      setAreaInfoList(list);
    },
    refreshAreaList: () => {
      let errorInfos: { blockGuid: string; errorTypes: string[]; errorValues: any[] }[] = [];

      // 先计算 errorInfos
      const updatedAreaInfoList = areaInfoList.map((area: AreaInfoType) => {
        const initInfo = initAssigneeData.find(
          i =>
            i.blockGuid === area.blockGuid &&
            i.assigneeType === 1 &&
            !personnelAccessContext?.isGovernments
        );
        let assigneeError = false;
        let areaError = false;
        const errorInfo = {
          blockGuid: area.blockGuid,
          errorTypes: [] as string[],
          errorValues: [] as any[],
        };

        if (initInfo && initInfo.assigneeList.length >= 1 && area.assigneeList?.length === 0) {
          assigneeError = true;
          errorInfo.errorTypes.push('assigneeError');
        }

        if (area.roomCategoryList.length >= 1) {
          areaError = !area.roomCategoryList.some(i => i.checked);
          if (areaError) {
            errorInfo.errorTypes.push('areaError');
          }
        }

        errorInfos.push(errorInfo);
        return { ...area, assigneeError, areaError };
      });

      setAreaInfoList(updatedAreaInfoList);

      return errorInfos; // 现在返回的是最新的计算结果
    },
  }));

  React.useEffect(() => {
    const updatedCheckValueList = checkValueListRef.current?.map(checkItem => {
      const flatRoomCategoryList = areaInfoList.map(i => i.roomCategoryList).flat();
      if (flatRoomCategoryList.length === 0) {
        return checkItem;
      }

      // 判断是否存在至少一个未选中的项
      const hasUnchecked = flatRoomCategoryList
        .filter(i => i.category === checkItem.category)
        .some(i => !i.checked);
      if (hasUnchecked) {
        return { ...checkItem, check: false };
      }

      // 判断是否所有的项都已选中
      const allChecked = flatRoomCategoryList
        .filter(i => i.category === checkItem.category)
        .every(i => i.checked);
      if (allChecked) {
        return { ...checkItem, check: true };
      }

      return checkItem;
    });
    setCheckValueList(updatedCheckValueList);
  }, [areaInfoList]);

  // 检查是否应该禁用复选框
  const isCheckboxDisabled = (blockInfo: {
    type: string;
    label: string;
    value: string;
    metaSubType: string;
  }) => {
    // 当业务分类 visitType.value[0] 是基础设施、物理安全、物业、其他的时候
    // 并且 selectedValues 该项里有包含一个 机房类型 metaSubType === 'IDC_BLOCK'
    //非 (政府 商务标签) 存在机房楼 则需要禁用掉 initBlocks里面  value ===“PZ’|"Z"的机房 并携带 'PZ'
    //政府 商务标签 禁用'PZ' 携带'PZ'
    // 不存在机房楼 且存在 变电站 禁用'PZ' 携带'PZ' ，
    const _list = blockCheckListRef.current?.onRecognize() || areaInfoList.map(i => i.blockGuid);
    const visitTypeValue = personnelAccessContext?.visitType?.value?.[0];
    const isInfrastructure = personnelAccessContext?.initDisableVisitTypes.includes(visitTypeValue);

    // 检查 selectedValues 中是否已经包含了一个机房类型（IDC_BLOCK）
    const hasSelectedIdcBlock = memoInitBlocks.some(
      block => _list.includes(block.value) && block.metaSubType === 'IDC_BLOCK'
    );

    // 是否存在变电站类型（SUBSTATION）
    const hasSubstation = memoInitBlocks.some(
      block => _list?.includes(block.value) && block.metaSubType === 'POWER_STATION'
    );

    // 检查当前项的 value 末尾是否是 'PZ' 或 'Z'
    const blockValue = blockInfo.value;
    const segments = blockValue.split('.');
    const lastSegment = segments[segments.length - 1];

    // 是否是需要特殊处理的 “机房类型块”
    const isDatacenterType =
      personnelAccessContext?.isSpecialtyVisitType || (hasSubstation && !hasSelectedIdcBlock)
        ? lastSegment === 'PZ'
        : lastSegment === 'PZ' || lastSegment === 'Z';

    // 如果满足所有条件，则禁用该复选框
    // ✅ 核心禁用判断逻辑
    const shouldDisable = (() => {
      // 非政府 / 商务标签 且存在机房楼，禁用 PZ 或 Z
      if (
        !personnelAccessContext?.isSpecialtyVisitType &&
        hasSelectedIdcBlock &&
        isDatacenterType &&
        isInfrastructure
        //  &&!hasSubstation
      ) {
        return true;
      }

      // 政府 / 商务标签 且存在机房楼，禁用 PZ
      if (
        personnelAccessContext?.isSpecialtyVisitType &&
        hasSelectedIdcBlock &&
        isDatacenterType &&
        isInfrastructure
        // &&!hasSubstation
      ) {
        // 政府商务
        return true;
      }

      // 不存在机房楼，存在变电站，禁用 PZ
      if (!hasSelectedIdcBlock && hasSubstation && isDatacenterType && isInfrastructure) {
        return true;
      }
      return false;
    })();
    return shouldDisable;
  };
  // 更新 Context 中的 disableBlocks 字段
  React.useEffect(() => {
    // 计算禁用的楼栋列表
    const disabledBlocks = memoInitBlocks
      .filter(blockInfo => isCheckboxDisabled(blockInfo))
      .map(blockInfo => blockInfo.value);

    // 更新到 Context 中
    if (personnelAccessContext?.updateData) {
      personnelAccessContext.updateData({ disableBlocks: disabledBlocks });
    }
  }, [memoInitBlocks, areaInfoList, personnelAccessContext?.visitType]);

  return (
    <div style={{ position: 'relative' }}>
      {/* 告警文案  */}
      {areaInfoList?.length >= 1 && (
        <Block style={{ padding: '12px 16px' }}>
          <Space
            direction="vertical"
            style={{
              width: 'calc(100% - 24px)',
              flex: 1,
              padding: '12px',
              borderRadius: '4px',
              color: alarmColor,
              background: 'rgba(255, 241, 240, 1)',
            }}
          >
            <Space style={{ lineHeight: '22px' }}>
              <WarningFilled
                style={{
                  paddingRight: '8px',
                  color: alarmColor,
                  fontSize: '16px',
                }}
              />
              <Typography.Text style={{ color: alarmColor, fontWeight: 500 }}>
                管控等级说明
              </Typography.Text>
            </Space>
            <Space direction="vertical" style={{ lineHeight: '20px' }}>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Typography.Title style={{ fontSize: '12px', fontWeight: 500 }}>
                  一级
                </Typography.Title>
                <Tag
                  style={{
                    '--background-color': tagBgColor,
                    '--border-color': tagBgColor,
                    '--text-color': tagColor,
                    width: '45px',
                    padding: '2px 8px',
                    margin: '0 8px',
                    textAlign: 'center',
                    borderRadius: '4px',
                  }}
                >
                  IT
                </Tag>
                <Typography.Text type="weak" style={{ fontSize: '12px' }}>
                  IT设施区(数据机房/IT库房) ，审批至客户；
                </Typography.Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Typography.Title style={{ fontSize: '12px', fontWeight: 500 }}>
                  二级
                </Typography.Title>
                <Tag
                  style={{
                    '--background-color': tagBgColor,
                    '--border-color': tagBgColor,
                    '--text-color': tagColor,
                    width: '45px',
                    padding: '2px 8px',
                    margin: '0 8px',
                    textAlign: 'center',
                    borderRadius: '4px',
                  }}
                >
                  FM
                </Tag>
                <Typography.Text type="weak" style={{ fontSize: '12px' }}>
                  基础设施区，审批至园区经理；
                </Typography.Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Typography.Title style={{ fontSize: '12px', fontWeight: 500 }}>
                  三级
                </Typography.Title>
                <Tag
                  style={{
                    '--background-color': tagBgColor,
                    '--border-color': tagBgColor,
                    '--text-color': tagColor,
                    width: '45px',
                    padding: '2px 8px',
                    margin: '0 8px',
                    textAlign: 'center',
                    borderRadius: '4px',
                  }}
                >
                  Office
                </Tag>
                <Typography.Text type="weak" style={{ fontSize: '12px' }}>
                  办公区，审批至机房经理；
                </Typography.Text>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <Typography.Title style={{ fontSize: '12px', fontWeight: 500 }}>
                  四级
                </Typography.Title>
                <Tag
                  style={{
                    '--background-color': tagBgColor,
                    '--border-color': tagBgColor,
                    '--text-color': tagColor,
                    width: '45px',
                    padding: '2px 8px',
                    margin: '0 8px',
                    textAlign: 'center',
                    borderRadius: '4px',
                  }}
                >
                  Other
                </Tag>
                <Typography.Text type="weak" style={{ fontSize: '12px' }}>
                  楼内其他区，审批至机房经理；
                </Typography.Text>
              </div>
            </Space>
          </Space>
        </Block>
      )}
      <StyledPopup
        bodyStyle={{
          height: 'auto',
          maxHeight: '66vh',
        }}
        visible={blockPopupVisible}
        autoClose={false}
        footer={
          <div
            style={{
              margin: '16px',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Button
              block
              fill="outline"
              color="primary"
              style={{ width: '100%', marginRight: '12px', fontSize: '14px' }}
              onClick={() => {
                blockCheckListRef.current?.onResetBlocks();
              }}
            >
              重置
            </Button>
            <Button
              block
              style={{ width: '100%', fontSize: '14px' }}
              color="primary"
              onClick={onAddBlocks}
            >
              确认
            </Button>
          </div>
        }
        position="bottom"
        title="添加楼栋"
        destroyOnClose
        contentStyle={{ padding: '8px 0 0 0' }}
        onMaskClick={() => {
          setBlockPopupVisible(false);
        }}
        onClose={() => {
          setBlockPopupVisible(false);
        }}
      >
        <BlockCheckList
          initBlocks={memoInitBlocks}
          blockCheckListRef={blockCheckListRef}
          areaInfoList={areaInfoList}
        />
      </StyledPopup>

      <div>
        {memoCategoryList.some(category => category) && (
          <>
            {areaAlertTips.open && (
              <div
                style={{
                  width: '174px',
                  height: '54px',
                  padding: '16px',
                  marginTop: '0',
                  position: 'absolute',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  zIndex: 11,
                  borderRadius: '8px',
                  background: '#fff',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                }}
              >
                <div style={{ display: 'flex' }}>
                  <ExclamationCircleOutlined
                    style={{
                      marginRight: '8px',
                      fontSize: '14px',
                      color: warningColor,
                    }}
                  />
                  <Typography.Text>请选择各楼进入区域</Typography.Text>
                </div>
                <div style={{ display: 'flex', justifyContent: 'end', marginTop: '8px' }}>
                  <Button
                    style={{
                      width: '72px',
                      height: '24px',
                      padding: 0,
                      marginRight: '8px',
                      fontSize: '14px',
                    }}
                    block
                    onClick={() => {
                      setAreaAlertTips({ open: false, value: 'NoMoreTips' });
                    }}
                  >
                    不再提醒
                  </Button>
                  <Button
                    style={{
                      width: '72px',
                      height: '24px',
                      padding: 0,
                      fontSize: '14px',
                    }}
                    block
                    color="primary"
                    onClick={() => {
                      setAreaAlertTips({ open: false, value: 'No' });
                    }}
                  >
                    我知道了
                  </Button>
                </div>
              </div>
            )}
            <div
              style={{
                display: 'flex',
                margin: '0 16px',
                padding: '12px 0px',
                borderBottom: '1px solid rgba(242, 246, 251, 1)',
              }}
            >
              <Space
                style={{
                  width: '100%',
                  height: '22px',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  lineHeight: '22px',
                }}
              >
                <Space style={{ width: '100%', alignItems: 'center' }}>
                  <div style={{ width: '45px' }}>
                    <Typography.Title style={{ fontSize: '14px' }}>全选</Typography.Title>
                  </div>
                  <Space style={{ width: '100%', alignItems: 'center' }}>
                    <Checkbox.Group
                      // @ts-ignore
                      value={checkValueList.filter(item => item.check).map(item => item.category)}
                      onChange={checkedValues => {
                        const updatedCheckValueList = memoCategoryList.map(category => {
                          return {
                            category,
                            check: category ? checkedValues.includes(category) : false,
                          };
                        });
                        checkValueListRef.current = updatedCheckValueList;
                        setCheckValueList(updatedCheckValueList);
                      }}
                    >
                      {memoCategoryList.map(
                        category =>
                          category && (
                            <StyledCheckbox
                              key={category}
                              onChange={isChecked => {
                                onAllCheckCategory({ category, check: isChecked });
                              }}
                              value={category}
                            >
                              <Typography.Text style={{ fontSize: '12px' }}>
                                {category}
                              </Typography.Text>
                            </StyledCheckbox>
                          )
                      )}
                    </Checkbox.Group>
                  </Space>
                </Space>

                <>
                  <Popover content="请选择各楼进入区域" trigger="click" mode="dark">
                    <QuestionCircleOutlined
                      style={{
                        position: 'relative',
                        top: '-1px',
                        fontSize: '16px',
                        color: 'rgba(134, 144, 156, 1)',
                      }}
                    />
                  </Popover>
                </>
              </Space>
              <Divider />
            </div>
          </>
        )}
        {areaInfoList.map(areaInfo => {
          if (personnelAccessContext?.disableBlocks?.includes(areaInfo.blockGuid)) {
            return null;
          }
          return (
            <SwipeAction
              key={areaInfo.blockGuid}
              closeOnAction={false}
              rightActions={[
                {
                  text: '删除',
                  key: 'delete',
                  color: 'danger',
                  onClick: e => {
                    onDelete(areaInfo);
                  },
                },
              ]}
            >
              <div
                style={{
                  display: 'flex',
                  margin: '0 16px',
                  padding:
                    areaInfo.assigneeError || areaInfo.areaError ? '12px 0px 26px 0' : '12px 0',
                  borderBottom: '1px solid rgba(242, 246, 251, 1)',
                }}
              >
                <div
                  style={{
                    width: '100%',
                    display: 'grid',
                    position: 'relative',
                    gridTemplateColumns: 'auto 84px',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      marginRight: '8px',
                    }}
                  >
                    <div
                      style={{
                        minWidth: '45px',
                        maxWidth: '98px',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      <Typography.Title style={{ fontSize: '14px' }}>
                        {areaInfo.blockLabel}
                      </Typography.Title>
                    </div>
                    <div style={{ position: 'relative', flex: 1 }}>
                      <StyledSelector
                        multiple
                        value={areaInfo.roomCategoryList
                          .filter(categoryInfo => categoryInfo.checked)
                          .map(categoryInfo => categoryInfo.category)}
                        options={areaInfo.roomCategoryList.map(categoryInfo => ({
                          label: categoryInfo.category,
                          value: categoryInfo.category,
                        }))}
                        count={areaInfo.roomCategoryList.length}
                        // @ts-ignore
                        onChange={(arr: string[]) => {
                          onCheckCategory(arr, areaInfo);
                        }}
                      />
                      {areaInfo.areaError && (
                        <Typography.Text
                          style={{
                            fontSize: '12px',
                            color: 'rgba(255, 49, 65, 1)',
                            position: 'absolute',
                            left: '2px',
                            top: '22px',
                          }}
                        >
                          进入区域必填
                        </Typography.Text>
                      )}
                    </div>
                  </div>

                  {initAssigneeData.find(
                    i =>
                      i.blockGuid === areaInfo.blockGuid &&
                      i.assigneeType === 1 &&
                      !personnelAccessContext?.isGovernments
                  )?.assigneeList?.length >= 1 ? (
                    <div style={{ display: 'flex', width: '84px', position: 'relative' }}>
                      {Array.isArray(areaInfo.assigneeList) && areaInfo.assigneeList.length >= 1 ? (
                        <div style={{ width: '100%', display: 'flex', justifyContent: 'end' }}>
                          <div
                            style={{
                              width: '100%',
                              marginTop: '1px',
                              display: 'flex',
                              justifyContent: 'end',
                            }}
                            onClick={() => {
                              setAssigneeDetailVisible({
                                key: areaInfo.blockGuid,
                                value: true,
                                assigneeList: areaInfo.assigneeList,
                              });
                            }}
                          >
                            <div style={{ position: 'relative' }}>
                              {areaInfo.assigneeList.slice(0, 3).map(({ id }, index) => (
                                <div
                                  key={id}
                                  style={{
                                    position: 'absolute',
                                    top: '2px',
                                    right: `${index * 13}px`, // 控制重叠程度，与设计图中的效果相似
                                    zIndex: (areaInfo.assigneeList?.length || 0) - index,
                                  }}
                                >
                                  <UserAvatar id={id} />
                                </div>
                              ))}
                            </div>
                            {areaInfo.assigneeList.length > 3 && (
                              <Typography.Text
                                style={{
                                  fontSize: '14px',
                                  lineHeight: '22px',
                                  color: 'var(--adm-color-primary)',
                                }}
                              >
                                <Typography.Text
                                  style={{
                                    fontSize: '14px',
                                    position: 'relative',
                                    top: '-3px',
                                    color: 'var(--adm-color-primary)',
                                  }}
                                >
                                  +
                                </Typography.Text>
                                {areaInfo.assigneeList.length}
                              </Typography.Text>
                            )}
                          </div>

                          <RightOutlined
                            style={{
                              position: 'relative',
                              marginLeft: '8px',
                              fontSize: '16px',
                              top: '-1px',
                              color: 'rgba(198, 202, 209, 1)',
                            }}
                            onClick={() => {
                              setAssigneeInfo({
                                ...assigneeInfo,
                                blockGuid: areaInfo.blockGuid,
                                visible: true,
                              });
                            }}
                          />
                        </div>
                      ) : (
                        <div
                          style={{ display: 'flex' }}
                          onClick={() => {
                            setAssigneeInfo({
                              ...assigneeInfo,
                              blockGuid: areaInfo.blockGuid,
                              visible: true,
                            });
                          }}
                        >
                          <Input
                            style={{ '--text-align': 'right' }}
                            placeholder="对接人"
                            readOnly
                          />
                          <RightOutlined
                            style={{
                              marginLeft: '8px',
                              fontSize: '16px',
                              color: 'rgba(198, 202, 209, 1)',
                            }}
                          />
                          {areaInfo.assigneeError && (
                            <Typography.Text
                              style={{
                                fontSize: '12px',
                                color: 'rgba(255, 49, 65, 1)',
                                position: 'absolute',
                                left: '8px',
                                top: '22px',
                              }}
                            >
                              对接人必填
                            </Typography.Text>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div style={{ display: 'flex', width: '84px' }}></div>
                  )}
                </div>
                <Divider />
              </div>
            </SwipeAction>
          );
        })}
        {assigneeInfo.visible && (
          <AssigneeCheckList
            assigneeInfo={assigneeInfo}
            setAssigneeInfo={setAssigneeInfo}
            areaInfoList={areaInfoList}
            setAreaInfoList={setAreaInfoList}
          />
        )}
        {assigneeDetailVisible.value && (
          <Popup
            visible={assigneeDetailVisible.value}
            title={'对接人'}
            onMaskClick={() => {
              setAssigneeDetailVisible({ key: '', value: false, assigneeList: [] });
            }}
            contentStyle={{ height: 'auto', overflow: 'auto', padding: '8px 0 0 0' }}
            bodyStyle={{
              height: 'auto',
              minHeight: '32vh',
              maxHeight: '50vh',
            }}
            footer={null}
          >
            <Block style={{ padding: '16px 16px 24px 16px', height: 'auto', minHeight: '32vh' }}>
              <Space size="large" wrap>
                {assigneeDetailVisible.assigneeList?.map(({ id, userName }) => (
                  <Space size="small" style={{ alignItems: 'center' }}>
                    <UserAvatar id={id} />
                    <Typography.Text>{userName}</Typography.Text>
                  </Space>
                ))}
              </Space>
            </Block>
          </Popup>
        )}

        <RoomTreePicker
          idc={idc}
          roomTreePickerRef={roomTreePickerRef}
          areaInfoList={areaInfoList}
          setAreaInfoList={setAreaInfoList}
          roomTreePickerStatuses={roomTreePickerStatuses}
          translate={translate}
        />
      </div>
    </div>
  );
}
function BlockCheckList({
  initBlocks,
  blockCheckListRef,
  areaInfoList,
}: {
  initBlocks: { type: string; label: string; value: string; metaSubType: string }[];
  blockCheckListRef: any;
  areaInfoList: AreaInfoType[];
}) {
  const [selectedValues, setSelectedValues] = React.useState<string[]>(
    areaInfoList ? areaInfoList.map(i => i.blockGuid) : []
  );

  React.useImperativeHandle(blockCheckListRef, () => ({
    onResetBlocks: () => {
      setSelectedValues([]);
    },
    onDeleteBlock: ({ blockGuid, editStatus }: { blockGuid: string; editStatus?: boolean }) => {
      setSelectedValues(preState => preState.filter(v => v !== blockGuid));
    },
    onRecognize: () => {
      return selectedValues;
    },
  }));
  const dialogRef = React.useRef<any>(null); // 用于获取弹窗的引用
  const formContext = usePersonnelAccessFormContext();
  // 检查是否应该禁用复选框
  const isCheckboxDisabled = (blockInfo: {
    type: string;
    label: string;
    value: string;
    metaSubType: string;
  }) => {
    // 当业务分类 visitType.value[0] 是基础设施、物理安全、物业、其他的时候
    // 并且 selectedValues 该项里有包含一个 机房类型 metaSubType === 'IDC_BLOCK'
    //非 (政府 商务标签) 存在机房楼 则需要禁用掉 initBlocks里面  value ===“PZ’|"Z"的机房 并携带 'PZ'
    //政府 商务标签 禁用'PZ' 携带'PZ'
    // 不存在机房楼 且存在 变电站 禁用'PZ' 携带'PZ' ，

    const visitTypeValue = formContext?.visitType?.value?.[0];
    const isInfrastructure = formContext?.initDisableVisitTypes.includes(visitTypeValue);

    // 检查 selectedValues 中是否已经包含了一个机房类型（IDC_BLOCK）
    const hasSelectedIdcBlock = initBlocks.some(
      block => selectedValues.includes(block.value) && block.metaSubType === 'IDC_BLOCK'
    );

    // 是否存在变电站类型（SUBSTATION）
    const hasSubstation = initBlocks.some(
      block => selectedValues.includes(block.value) && block.metaSubType === 'POWER_STATION'
    );

    // 检查当前项的 value 末尾是否是 'PZ' 或 'Z'
    const blockValue = blockInfo.value;
    const segments = blockValue.split('.');
    const lastSegment = segments[segments.length - 1];

    // 是否是需要特殊处理的 “机房类型块”
    const isDatacenterType =
      formContext?.isSpecialtyVisitType || (hasSubstation && !hasSelectedIdcBlock)
        ? lastSegment === 'PZ'
        : lastSegment === 'PZ' || lastSegment === 'Z';

    // 如果满足所有条件，则禁用该复选框
    // ✅ 核心禁用判断逻辑
    const shouldDisable = (() => {
      // 非政府 / 商务标签 且存在机房楼，禁用 PZ 或 Z
      if (
        !formContext?.isSpecialtyVisitType &&
        hasSelectedIdcBlock &&
        isDatacenterType &&
        isInfrastructure
        // &&!hasSubstation
      ) {
        return true;
      }

      // 政府 / 商务标签 且存在机房楼，禁用 PZ
      if (
        formContext?.isSpecialtyVisitType &&
        hasSelectedIdcBlock &&
        isDatacenterType &&
        isInfrastructure
        // &&!hasSubstation
      ) {
        // 政府商务
        return true;
      }

      // 不存在机房楼，存在变电站，禁用 PZ
      if (!hasSelectedIdcBlock && hasSubstation && isDatacenterType && isInfrastructure) {
        return true;
      }
      return false;
    })();
    return shouldDisable;
    // return isInfrastructure && hasSelectedIdcBlock && isDatacenterType;
  };

  const handleItemClick = React.useCallback(
    (value: string, disabled: boolean) => {
      // 如果禁用则不处理点击
      if (disabled) return;

      setSelectedValues((prev: any) => {
        if (prev.includes(value)) {
          const selectedItem = areaInfoList.find(
            i =>
              i.blockGuid === value &&
              ((i.assigneeList && i.assigneeList?.length >= 1) ||
                i.roomCategoryList.some(i => i.checked))
          );
          if (selectedItem) {
            dialogRef.current = Dialog.show({
              title: false,
              content: (
                <div style={{ paddingTop: '16px' }}>
                  <div style={{ height: '44px', display: 'flex' }}>
                    <ExclamationCircleOutlined
                      style={{
                        marginRight: '8px',
                        fontSize: '14px',
                        color: warningColor,
                      }}
                    />
                    <Typography.Text>
                      当前楼栋已配置进入区域/对接人，取消将会清空已选择内容
                    </Typography.Text>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'end', marginTop: '8px' }}>
                    <Button
                      style={{
                        width: '72px',
                        height: '22px',
                        padding: 0,
                        marginRight: '8px',
                        fontSize: '14px',
                      }}
                      block
                      onClick={() => {
                        if (dialogRef.current) {
                          dialogRef.current.close(); // 手动关闭弹窗
                        }
                      }}
                    >
                      我再想想
                    </Button>
                    <Button
                      style={{ width: '72px', height: '22px', padding: 0, fontSize: '14px' }}
                      block
                      color="primary"
                      onClick={() => {
                        setSelectedValues(prevValues => prevValues.filter(item => item !== value));
                        if (dialogRef.current) {
                          dialogRef.current.close(); // 手动关闭弹窗
                        }
                      }}
                    >
                      确认取消
                    </Button>
                  </div>
                </div>
              ),
              closeOnAction: false, // 让弹窗不自动关闭
              bodyStyle: { paddingTop: 0 },
            });
            return prev; // 当前不更新，等待用户确认
          } else {
            return prev.filter((item: string) => item !== value);
          }
        }
        // return prev.includes(value) ? prev.filter(item => item !== value) : [...prev, value];
        return [...prev, value];
      });
    },
    [areaInfoList, selectedValues]
  );
  React.useEffect(() => {
    if (Array.isArray(areaInfoList)) {
      setSelectedValues(areaInfoList.map(i => i.blockGuid));
    }
  }, []);

  // 更新 Context 中的 disableBlocks 字段
  React.useEffect(() => {
    // 计算禁用的楼栋列表
    const disabledBlocks = initBlocks
      .filter(blockInfo => isCheckboxDisabled(blockInfo))
      .map(blockInfo => blockInfo.value);

    // 更新到 Context 中
    if (formContext?.updateData) {
      formContext.updateData({ disableBlocks: disabledBlocks });
    }
  }, [initBlocks, selectedValues, formContext?.visitType]);

  return (
    <>
      <Checkbox.Group value={selectedValues}>
        <List>
          {initBlocks.map(blockInfo => {
            const disabled = isCheckboxDisabled(blockInfo);
            return (
              <List.Item
                key={blockInfo.value}
                onClick={(e: { stopPropagation: () => void }) => {
                  e.stopPropagation();
                  handleItemClick(blockInfo.value, !!disabled);
                }}
                arrow={false}
                style={disabled ? { opacity: 0.6, cursor: 'not-allowed' } : undefined}
              >
                <Space>
                  <Checkbox value={blockInfo.value} disabled={disabled} />
                  <Typography.Title style={{ fontSize: '16px', fontWeight: 400 }}>
                    {blockInfo.label}
                    {/* {disabled && (
                      <Typography.Text
                        type="secondary"
                        style={{ fontSize: '12px', marginLeft: '4px' }}
                      >
                        (不可选)
                      </Typography.Text>
                    )} */}
                  </Typography.Title>
                </Space>
              </List.Item>
            );
          })}
        </List>
      </Checkbox.Group>
    </>
  );
}
function AssigneeCheckList({
  assigneeInfo,
  setAssigneeInfo,
  areaInfoList,
  setAreaInfoList,
}: {
  assigneeInfo: {
    visible: boolean;
    blockGuid: string | undefined;
  };
  areaInfoList: AreaInfoType[];
  setAreaInfoList: (v: any) => void;
  setAssigneeInfo: (v: any) => void;
}) {
  const { visible, blockGuid } = assigneeInfo;
  const { assigneeList } = areaInfoList.find(i => i.blockGuid === blockGuid) || {
    assigneeList: [],
  };
  const allAssigneeRef = React.useRef([]);

  const [searchValue, setSearchValue] = React.useState();
  const [state, setState] = React.useState<{ checkValues: number[] }>({
    checkValues: assigneeList?.map(i => i?.id) || [],
  });

  const [getAssigneeConfig, { loading: assigneeConfigLoading }] = useUpdateAssigneeConfigMutation();

  const filteredItems = React.useMemo(() => {
    if (searchValue) {
      return allAssigneeRef.current.filter(({ label }) =>
        (label as string[])?.includes(searchValue)
      );
    }
    return allAssigneeRef.current;
  }, [allAssigneeRef.current, searchValue]);

  React.useEffect(() => {
    // 查当前楼栋下拥有对接人
    if (blockGuid) {
      (async () => {
        getAssigneeConfig({
          variables: {
            query: {
              blockGuid,
              customerNo: 'SYSTEM',
            },
          },
          onCompleted(rspData) {
            // 楼栋下配置的对接人
            if (rspData) {
              const { data } = rspData?.updateChangeAssigneeConfig;

              if (data) {
                const _assigneeList =
                  data
                    .filter(i => i?.assigneeType === 1)
                    .map(item => item?.assigneeList)
                    .flat() || [];
                const tackleData = _assigneeList
                  .map(assignee => ({
                    value: assignee?.id,
                    label: assignee?.userName,
                  }))
                  ?.reduce((acc, item) => {
                    if (!acc.some(existingItem => existingItem.value === item.value)) {
                      acc.push(item);
                    }
                    return acc;
                  }, []);
                allAssigneeRef.current = tackleData;
              }
            }
          },
        });
      })();
    }
  }, [blockGuid]);

  return (
    <>
      <Popup
        title="添加对接人"
        visible={visible}
        onMaskClick={() =>
          setAssigneeInfo((preState: any) => ({
            ...preState,
            visible: false,
          }))
        }
        bodyStyle={{ height: '80vh' }}
        contentStyle={{ padding: '0' }}
        getContainer={null}
        footer={
          <div
            style={{
              margin: '16px',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <Button
              block
              fill="outline"
              color="primary"
              style={{ width: '100%', marginRight: '12px' }}
              onClick={() => {
                setState({ checkValues: [] });
                // setAreaInfoList((preState: any) =>
                //   preState.map((i: AreaInfoType) => {
                //     if (i.blockGuid === blockGuid) {
                //       return { ...i, assigneeList: [] };
                //     }
                //     return i;
                //   })
                // );
              }}
            >
              重置
            </Button>
            <Button
              block
              style={{ width: '100%' }}
              color="primary"
              onClick={() => {
                setAssigneeInfo({
                  ...assigneeInfo,
                  visible: false,
                });

                setAreaInfoList((preState: any) =>
                  preState.map((i: AreaInfoType) => {
                    if (i.blockGuid === blockGuid) {
                      return {
                        ...i,
                        assigneeList: allAssigneeRef.current
                          .map(({ value, label }: { value: number; label: string }) => {
                            if (state.checkValues.includes(value)) {
                              return { id: value, userName: label };
                            }
                          })
                          .filter(Boolean),
                      };
                    }
                    return i;
                  })
                );
              }}
            >
              确定
            </Button>
          </div>
        }
      >
        <Block style={{ padding: 0 }}>
          <StyledSearchBar
            placeholder="请输入姓名查询"
            value={searchValue}
            // @ts-ignore
            onChange={setSearchValue}
          />
          <div style={{ height: 'auto', overflowY: 'scroll', padding: '0' }}>
            <CheckList
              multiple
              value={state.checkValues}
              onChange={(v: number[]) => {
                setState({ ...state, checkValues: v });
              }}
              style={{ minHeight: '40vh' }}
            >
              {filteredItems.map((assigneeInfo: { value: number; label: string }) => (
                <CheckList.Item key={assigneeInfo.value} value={assigneeInfo.value}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Typography.Text
                      style={
                        state.checkValues.find((i: number) => i === assigneeInfo.value)
                          ? { fontSize: '16px', color: tagColor }
                          : { fontSize: '16px' }
                      }
                    >
                      {assigneeInfo.label}
                    </Typography.Text>
                  </div>
                </CheckList.Item>
              ))}
            </CheckList>
          </div>
        </Block>
      </Popup>
    </>
  );
}

function RoomTreePicker({
  idc,
  roomTreePickerRef,
  roomTreePickerStatuses,
  areaInfoList,
  setAreaInfoList,
  translate,
}: {
  idc: string;
  roomTreePickerRef: any;
  roomTreePickerStatuses: any;
  areaInfoList: AreaInfoType[];
  setAreaInfoList: (v: any) => void;
  translate: (v: any) => any;
}) {
  const { blockGuid, blockLabel, category, categoryLabel } = roomTreePickerStatuses;

  const roomList = React.useMemo(() => {
    const info = areaInfoList.find(i => i.blockGuid === blockGuid);
    if (info) {
      //   [
      //   {
      //     label: '111备品备件间 SXDTYG_D1_111备品备件间',
      //     parentValue: 'sxdtyg.1.IT.61',
      //     type: 'ROOM',
      //     value: 'sxdtyg.1.111备品备件间',
      //     custom: { type: 'IT_ROOM', typeName: 'IT机房' },
      //   },
      // ]
      return info.roomCategoryList.find(i => i.isChildren)?.roomList;
    }
    return [];
  }, [areaInfoList]);

  return (
    <>
      {/* @ts-ignore */}
      <SpaceTreePicker
        ref={roomTreePickerRef}
        title="进入区域"
        idc={idc}
        blocks={blockGuid && [(blockGuid as string).split('.')[1]]}
        showCheckedStrategy="SHOW_CHILD"
        nodeTypes={['ROOM_CATEGORY', 'ROOM_TYPE', 'ROOM']}
        queryRoomProperties
        onlyEnableBlock
        onlyEnableRoom
        unmixedPopup
        susceptibleTips
        // rootLabel={`${blockLabel} ${categoryLabel}`}
        rootLabel={categoryLabel}
        footerBtnType="reset"
        value={roomList}
        onChange={(values, options) => {
          const _options = options.map(i => ({
            type: i.type,
            value: i.value,
            label: i.label,
            parentValue: i.parentValue,
            custom: i.custom,
          }));
          setAreaInfoList((perState: any) => {
            const areaCheckList = perState.map((area: any) => {
              if (area.blockGuid === blockGuid) {
                return {
                  ...area,
                  roomCategoryList: area.roomCategoryList.map((i: any) => {
                    return i.category === category
                      ? { ...i, checked: !!(_options.length >= 1), roomList: _options }
                      : i;
                  }),
                };
              }
              return area;
            });
            // 校验
            const updatedAreaInfoList = translate(areaCheckList);
            return updatedAreaInfoList;
          });
        }}
      />
    </>
  );
}
