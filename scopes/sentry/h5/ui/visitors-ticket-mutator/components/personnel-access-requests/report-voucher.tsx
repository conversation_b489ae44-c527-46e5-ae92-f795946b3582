import { QuestionCircleOutlined } from '@ant-design/icons';
import React from 'react';
import styled from 'styled-components';

import { Form } from '@manyun/base-ui.h5.ui.form';
import { McUpload } from '@manyun/base-ui.h5.ui.mc-upload';
import { Popover } from '@manyun/base-ui.h5.ui.popover';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { openExternalLink } from '@manyun/dc-brain.h5.util.common';

const tagColor = 'rgba(184, 28, 34, 1)';
const StyledPopover = styled(Popover)`
  .adm-popover-inner-content {
    width: 300px;
    height: 48px;
    padding: 6px 8px;
    background: #162334bf;
    font-size: 14px;
    border-radius: 6px;
  }
`;
function ReportVoucher() {
  return (
    <Form.Item
      name="fileInfoList"
      label={
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography.Title
            style={{
              marginBottom: '8px',
              fontSize: '16px',
              lineHeight: '24px',
              fontWeight: 400,
            }}
          >
            报备凭证
            <StyledPopover
              content={
                <>进入生产区需完成客户机房参观报备，格式要求：审批链接：https://XXXXXXXXX.com</>
              }
              mode="dark"
              trigger="click"
            >
              <QuestionCircleOutlined
                style={{
                  marginLeft: '6px',
                  position: 'relative',
                  fontSize: '16px',
                  color: 'rgba(134, 144, 156, 1)',
                }}
              />
            </StyledPopover>
          </Typography.Title>
          <Typography.Text
            style={{ color: tagColor, fontSize: '14px' }}
            onClick={() => {
              openExternalLink('https://idc-service.bytedance.com/greatwall/external-visit/create');
            }}
          >
            客户平台报备链接
          </Typography.Text>
        </div>
      }
      rules={[
        {
          required: true,
          message: `报备凭证必须上传`,
        },
      ]}
    >
      <McUpload
        accept=".jpg,.jpeg,.png,.doc, .docx, .xls, .xlsx, .pdf"
        maxCount={10}
        maxFileSize={1000}
        deleteIconStyle={{ fontSize: '16px', textAlign: 'right' }}
      />
    </Form.Item>
  );
}
export function FormItemReportVoucher() {
  return <ReportVoucher />;
}
