import CloseOutlined from '@ant-design/icons/CloseOutlined';
import React, { useState } from 'react';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.h5.ui.button';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Typography } from '@manyun/base-ui.h5.ui.typography';

// 车牌号输入组件的属性类型
interface LicensePlateInputProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: (plateNumber: string) => void;
  defaultValue?: string;
}

// 样式化组件
const PlateContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  gap: 4px;
`;

const PlateChar = styled.div<{ active: boolean; isNewEnergy?: boolean }>`
  width: 40px;
  height: 50px;
  /* border: 2px solid ${props => (props.active ? '#1890ff' : '#d9d9d9')}; */
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  background-color: ${props => (props.active ? '#e6f7ff' : '#fff')};
  cursor: pointer;
  position: relative;

  ${props =>
    props.isNewEnergy &&
    `
    background: linear-gradient(135deg, #00B578 0%, #7BE1B5 100%);
    color: white;
  `}
`;

const PlateDot = styled.div`
  width: 8px;
  height: 8px;
  background-color: #333;
  border-radius: 50%;
`;

const NewEnergyToggle = styled.div<{ active: boolean }>`
  min-width: 32px;
  padding: 16px 4px;
  border: 1px solid ${props => (props.active ? '#d9d9d9' : '#7BE1B5')};
  border-radius: 6px;
  font-size: 10px;
  background-color: ${props => (props.active ? '#f6ffed' : '#fff')};
  color: ${props => (props.active ? '#666' : ' #00b578')};
  cursor: pointer;
  user-select: none;
`;

const KeyboardContainer = styled.div`
  background-color: #f5f5f5;
  padding: 8px;
  /* border-radius: 12px 12px 0 0; */
`;

const KeyboardRow = styled.div`
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
`;

const KeyboardKey = styled.button<{ special?: boolean }>`
  width: 100%;
  min-width: 28px;
  max-width: 34px;
  height: 40px;
  border: none;
  border-radius: 6px;
  background-color: ${props => (props.special ? '#1890ff' : '#fff')};
  color: ${props => (props.special ? '#fff' : '#333')};
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  &:active {
    transform: scale(0.95);
    background-color: ${props => (props.special ? '#096dd9' : '#f0f0f0')};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// 省份简称
const PROVINCES = [
  '京',
  '津',
  '冀',
  '晋',
  '蒙',
  '辽',
  '吉',
  '黑',
  '沪',

  '苏',
  '浙',
  '皖',
  '闽',
  '赣',
  '鲁',
  '豫',
  '鄂',
  '湘',

  '粤',
  '桂',
  '琼',
  '渝',
  '川',
  '贵',
  '云',
  '藏',

  '陕',
  '甘',
  '青',
  '宁',
  '新',
  '临',
];

// 字母（不包含I和O）
const LETTERS = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'J',
  'K',
  'L',
  'M',
  'N',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
];

// 数字
const NUMBERS = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];

// 特殊字符
const SPECIAL_CHARS = ['港', '澳', '警', '学', '军'];

export const LicensePlateInput: React.FC<LicensePlateInputProps> = ({
  visible,
  onClose,
  onConfirm,
  defaultValue = '',
}) => {
  // 解析默认值
  const parseDefaultValue = (value: string) => {
    if (!value) return ['', '', '', '', '', '', ''];
    const chars = value.split('');
    const result = ['', '', '', '', '', '', ''];
    for (let i = 0; i < Math.min(chars.length, 7); i++) {
      result[i] = chars[i];
    }
    return result;
  };

  const [plateChars, setPlateChars] = useState<string[]>(parseDefaultValue(defaultValue));
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isNewEnergy, setIsNewEnergy] = useState(false);
  const [newEnergyChar, setNewEnergyChar] = useState(''); // 新能源位的字符

  // 获取当前位置可用的键盘字符
  const getAvailableKeys = () => {
    if (currentIndex === 0) {
      return PROVINCES;
    } else if (currentIndex === 1) {
      return LETTERS;
    } else if (currentIndex === 7 && isNewEnergy) {
      // 新能源位只能输入数字和字母
      return [...NUMBERS, ...LETTERS];
    } else {
      return [...NUMBERS, ...LETTERS, ...SPECIAL_CHARS];
    }
  };

  // 处理键盘输入
  const handleKeyPress = (char: string) => {
    if (currentIndex === 7 && isNewEnergy) {
      // 新能源位输入
      setNewEnergyChar(char);
    } else {
      // 普通位输入
      const newPlateChars = [...plateChars];
      newPlateChars[currentIndex] = char;
      setPlateChars(newPlateChars);

      // 自动跳到下一位
      const maxLength = 7;
      if (currentIndex < maxLength - 1) {
        setCurrentIndex(currentIndex + 1);
      }
    }
  };

  // 处理删除
  const handleDelete = () => {
    if (currentIndex === 7 && isNewEnergy) {
      // 删除新能源位
      setNewEnergyChar('');
    } else {
      const newPlateChars = [...plateChars];
      if (newPlateChars[currentIndex]) {
        newPlateChars[currentIndex] = '';
      } else if (currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
        newPlateChars[currentIndex - 1] = '';
      }
      setPlateChars(newPlateChars);
    }
  };

  // 切换新能源车牌
  const toggleNewEnergy = () => {
    if (isNewEnergy && newEnergyChar) {
      // 如果已经是新能源状态且有输入字符，清空字符但保持新能源状态
      setIsNewEnergy(!isNewEnergy);
      setNewEnergyChar('');
      if (currentIndex === 7) {
        setCurrentIndex(6);
      }
    } else {
      // 切换新能源状态
      setIsNewEnergy(!isNewEnergy);
      setNewEnergyChar('');
      setCurrentIndex(7);
    }
  };

  // 确认输入
  const handleConfirm = () => {
    let plateNumber = plateChars.filter(char => char).join('');
    if (isNewEnergy && newEnergyChar) {
      plateNumber += newEnergyChar;
    }
    if (plateNumber.length >= 7) {
      onConfirm(plateNumber);
      onClose();
    }
  };

  // 左移光标
  const handleMoveCursorLeft = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  // 右移光标
  const handleMoveCursorRight = () => {
    const maxIndex = isNewEnergy ? 7 : 6;
    if (currentIndex < maxIndex) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  // 渲染键盘
  const renderKeyboard = () => {
    const keys = getAvailableKeys();
    const rows: string[][] = [];

    if (currentIndex === 0) {
      // 省份键盘布局
      rows.push(keys.slice(0, 9)); // 第一组：9个
      rows.push(keys.slice(9, 18)); // 第二组：9个
      rows.push(keys.slice(18, 26)); // 第三组：7个
      rows.push(keys.slice(26)); // 第四组：剩下的所有
    } else {
      // 字母数字键盘布局
      rows.push(NUMBERS);
      rows.push(['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P']);
      rows.push(['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L']);
      rows.push(['Z', 'X', 'C', 'V', 'B', 'N', 'M', ...SPECIAL_CHARS]);
    }
    rows[rows.length - 1]?.push('delete');
    return (
      <KeyboardContainer>
        {rows.map((row, rowIndex) => (
          <KeyboardRow key={rowIndex}>
            {row.map(key => {
              return (
                <KeyboardKey
                  key={key}
                  disabled={!keys.includes(key)}
                  onClick={() => {
                    if (key === 'delete') {
                      // handleDelete();
                      return;
                    }
                    handleKeyPress(key);
                  }}
                >
                  {key === 'delete' ? (
                    <div>
                      <CloseOutlined />
                    </div>
                  ) : (
                    key
                  )}
                </KeyboardKey>
              );
            })}
          </KeyboardRow>
        ))}
        <KeyboardRow>
          <KeyboardKey onClick={handleMoveCursorLeft} special>
            ←
          </KeyboardKey>
          <KeyboardKey onClick={handleMoveCursorRight} special>
            →
          </KeyboardKey>
        </KeyboardRow>
      </KeyboardContainer>
    );
  };

  return (
    <Popup
      visible={visible}
      onClose={onClose}
      title={
        <Typography.Text style={{ color: 'var(--adm-color-title)' }}>添加车辆</Typography.Text>
      }
      showCloseButton
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ padding: '6px' }}>
        {/* 车牌显示区域 */}
        <PlateContainer>
          {plateChars.slice(0, 7).map((char, index) => (
            <React.Fragment key={index}>
              <PlateChar active={currentIndex === index} onClick={() => setCurrentIndex(index)}>
                {char || ' '}
              </PlateChar>
              {index === 1 && <PlateDot />}
            </React.Fragment>
          ))}

          {/* 新能源区域 - 可以是按钮或输入框 */}
          {isNewEnergy ? (
            <PlateChar
              style={{ minWidth: '42px' }}
              active={currentIndex === 7}
              isNewEnergy
              onClick={() => {
                if (newEnergyChar || newEnergyChar === '') {
                  // 如果有字符，点击切换回按钮状态
                  toggleNewEnergy();
                }
              }}
            >
              {newEnergyChar || ' '}
            </PlateChar>
          ) : (
            <NewEnergyToggle active={false} onClick={toggleNewEnergy}>
              新能源
            </NewEnergyToggle>
          )}
        </PlateContainer>

        {/* 添加按钮 */}
        <Button
          block
          color="primary"
          disabled={plateChars.filter(char => char).length < 7}
          onClick={handleConfirm}
          style={{ marginBottom: '16px' }}
        >
          添加
        </Button>
      </div>

      {/* 自定义键盘 */}
      {renderKeyboard()}
    </Popup>
  );
};
