import dayjs from 'dayjs';
import isEqual from 'lodash.isequal';

/**
 * 格式化并对比两个表单数据对象
 * @param a 当前值
 * @param b 初始值
 * @returns boolean 是否不同（true 表示发生变化）
 */
export function isFormChanged(a: Record<string, any>, b: Record<string, any>) {
  const normalize = (data: Record<string, any>) => ({
    visitType: Array.isArray(data.visitType?.value) ? data.visitType.value : '',
    relatedTicketType: data.relatedTicketType ?? '',
    relatedTicketNumber: data.relatedTicketNumber ?? '',
    title: (data.title ?? '').trim(),
    purpose: (data.purpose ?? '').trim(),
    allowedStartTime:
      typeof data.allowedStartTime === 'number'
        ? data.allowedStartTime
        : data.allowedStartTime
        ? dayjs(data.allowedStartTime).valueOf()
        : '',
    allowedEndTime:
      typeof data.allowedEndTime === 'number'
        ? data.allowedEndTime
        : data.allowedEndTime
        ? dayjs(data.allowedEndTime).valueOf()
        : '',
    idc: data.idc ?? '',
    areaInfoList: (data.areaInfoList ?? []).map((f: any) => ({
      blockGuid: f.blockGuid,

      assigneeList: f.assigneeList?.map((i: any) => i.id).sort() ?? '',
      roomCategoryList: f.roomCategoryList?.map((i: any) => ({
        category: i.category,
        checked: i.checked,
        roomList: i.roomList?.map((i: any) => i.value).sort() ?? '',
      })),
    })),
    fileInfoList: (data.fileInfoList ?? []).map((f: any) => ({
      uid: f.uid,
      name: f.name,
      url: f.url,
    })),
    visitors: (data.visitors ?? []).map((v: any) => ({
      id: v.id,
      mobile: v.mobile
        ? {
            preValue: v.mobile.preValue,
            realValue: v.mobile.realValue,
          }
        : '',
      name: v.name ?? '',
      type: v.type?.[0] ?? '',
      gender: v.gender ?? '',
      identificationType: v.identificationType?.[0] ?? '',
      identification: v.identification ?? '',
      LPN: v.LPN ?? '',
      companyName: v.companyName ?? '',
      operationalAuthorization: v.operationalAuthorization ?? '',
      personalGoods: v.personalGoods ?? '',
      position: v.position ?? '',
    })),
    guestNum: data.guestNum ?? '',
    guestCompany: data.guestCompany ?? '',
    receptionName: data.receptionName ?? '',
    receptionPhone: data.receptionPhone ?? '',
  });

  return !isEqual(normalize(a), normalize(b));
}
