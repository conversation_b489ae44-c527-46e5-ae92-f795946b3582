import React, { useState } from 'react';
import { Form } from '@manyun/base-ui.h5.ui.form';
import { Input } from '@manyun/base-ui.h5.ui.input';
import { LicensePlateInput } from './license-plate-input';

interface LicensePlateFormItemProps {
  name?: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  rules?: any[];
}

export const LicensePlateFormItem: React.FC<LicensePlateFormItemProps> = ({
  name = 'licensePlate',
  label = '车牌号',
  placeholder = '点击输入车牌号',
  required = false,
  rules = []
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [plateValue, setPlateValue] = useState('');

  const handleInputClick = () => {
    setModalVisible(true);
  };

  const handleConfirm = (plateNumber: string) => {
    setPlateValue(plateNumber);
    // 这里可以通过 Form.useFormInstance() 来更新表单值
    // 或者通过 onChange 回调来处理
  };

  const defaultRules = required ? [
    {
      required: true,
      message: `${label}必填`,
    },
    ...rules
  ] : rules;

  return (
    <>
      <Form.Item
        name={name}
        label={label}
        rules={defaultRules}
      >
        <Input
          placeholder={placeholder}
          value={plateValue}
          readOnly
          onClick={handleInputClick}
          style={{ cursor: 'pointer' }}
        />
      </Form.Item>

      <LicensePlateInput
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onConfirm={handleConfirm}
        defaultValue={plateValue}
      />
    </>
  );
};

// 使用示例组件
export const LicensePlateExample: React.FC = () => {
  return (
    <Form>
      <LicensePlateFormItem
        name="vehicleLicense"
        label="车牌号码"
        placeholder="请输入车牌号"
        required
      />
    </Form>
  );
};
