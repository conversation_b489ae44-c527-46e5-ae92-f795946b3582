import React, { ReactNode, createContext, useCallback, useContext, useMemo, useState } from 'react';

export interface PersonnelAccessFormContextType {
  /**
   * 初始化需要禁用的楼栋 业务分类数据
   */
  initDisableVisitTypes: string[];
  /**
   * 初始化 业务分类数据为 政府 商务
   */
  initSpecialtyVisitTypes: string[];
  /**
   * 业务分类数据
   */
  visitType?: {
    value?: any[];
    options?: any[];
  };
  visitTypeInfo?: any;
  /**
   * 禁用的楼栋列表
   */
  disableBlocks: string[];
  /**
   * 是否为 政府 商务 特殊类型
   */
  isSpecialtyVisitType?: boolean;
  /**
   * 是否为 政府 类型
   */
  isGovernments?: boolean;
  updateData: (data: Partial<Omit<PersonnelAccessFormContextType, 'updateData'>>) => void;
}

const PersonnelAccessFormContext = createContext<PersonnelAccessFormContextType | null>(null);

// 创建 Provider 组件
export function PersonnelAccessFormContextProvider(props: { children: ReactNode; value?: any }) {
  // 使用 useState 管理共享的表单数据
  // 业务标签，商务：BUSINESS，政府：GOVERNMENT
  const [state, steState] = useState<Omit<PersonnelAccessFormContextType, 'updateData'>>({
    initDisableVisitTypes: [
      'dcim',
      'physical_security',
      'property',
      'business_visitor',
      'government_visitor',
      'other',
    ],
    initSpecialtyVisitTypes: ['GOVERNMENT', 'BUSINESS'],
    disableBlocks: [],
    visitType: undefined,
    visitTypeInfo: undefined,
  });

  // 是否为 政府 商务 特殊类型
  const isSpecialtyVisitType = useMemo(() => {
    return state.initSpecialtyVisitTypes.includes(state.visitTypeInfo?.tag ?? '');
  }, [state.visitTypeInfo, state.initSpecialtyVisitTypes]);
  // 是否为 政府 类型
  const isGovernments = useMemo(() => {
    return state.visitTypeInfo?.tag === 'GOVERNMENT';
  }, [state.visitTypeInfo]);

  // 更新表单数据的方法
  const updateData = (data: Partial<Omit<PersonnelAccessFormContextType, 'updateData'>>) => {
    steState(prevData => ({
      ...prevData,
      ...data,
    }));
  };

  return (
    <PersonnelAccessFormContext.Provider
      value={{ ...props.value, ...state, updateData, isSpecialtyVisitType, isGovernments }}
    >
      {props.children}
    </PersonnelAccessFormContext.Provider>
  );
}

export function usePersonnelAccessFormContext() {
  return useContext(PersonnelAccessFormContext);
}
