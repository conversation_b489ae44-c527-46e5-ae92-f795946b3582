import debounce from 'lodash.debounce';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.h5.ui.button';
import { CheckList } from '@manyun/base-ui.h5.ui.check-list';
import { Empty } from '@manyun/base-ui.h5.ui.empty';
import { Form } from '@manyun/base-ui.h5.ui.form';
import { Input } from '@manyun/base-ui.h5.ui.input';
import { Popup } from '@manyun/base-ui.h5.ui.popup';
import { SearchBar } from '@manyun/base-ui.h5.ui.search-bar';
import { Space } from '@manyun/base-ui.h5.ui.space';
import { Typography } from '@manyun/base-ui.h5.ui.typography';
import { useLazyUsersForUserSelect } from '@manyun/iam.gql.client.iam';

export const DivStyled = styled.div`
  .adm-popup .popupContext {
    background: inherit;
    .adm-search-bar-input-box {
      border-radius: 14px 14px;
    }
  }
`;
const StyledPopup = styled(Popup)`
  & .popupContext {
    padding: 16px;
    .adm-list-body .adm-list-item {
      padding-left: 2px;
      padding-right: 6px;
      background: none;
      .adm-list-item-content {
        padding-right: 0;
        margin-right: 0px;
      }
    }
  }
`;

interface FormItemUserSelectorProps {
  form: any;
}

export function FormItemUserSelector({ form, ...props }: FormItemUserSelectorProps) {
  const [fetchUsers, { data, loading }] = useLazyUsersForUserSelect();
  const watchReceptionName = form.getFieldValue('receptionName');
  const watchReceptionId = form.getFieldValue('receptionId');

  const [visible, setVisible] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  const [selectedId, setSelectedId] = useState<string | number | null>(null);
  const [customSelected, setCustomSelected] = useState(false); //区分系统外 或 内 用户
  const [serverList, setServerList] = useState<any>([]);

  // 根据是否有 searchValue 决定数据来源：如果在搜索，则用接口结果，否则用初始值构造
  const userList = useMemo(() => {
    if (searchValue) {
      return serverList;
    }
    if (watchReceptionId && watchReceptionName) {
      return [{ id: watchReceptionId, name: watchReceptionName }];
    }
    return [];
  }, [searchValue, serverList, watchReceptionId, watchReceptionName]);

  const debouncedFetchUsers = useMemo(() => {
    return debounce((val: string) => {
      fetchUsers({ variables: { key: val } });
    }, 300);
  }, [fetchUsers]);

  const handleInputChange = (value: string) => {
    const val = value?.trim();
    setSearchValue(val);

    if (val && val !== '') {
      debouncedFetchUsers(val);
    }

    if ((!val || val === '') && !watchReceptionId) {
      setSelectedId(null);
      setCustomSelected(false);
      setServerList([]);
    }
  };
  const handleSelect = (val: string | number, mode?: string) => {
    setSelectedId(val);
    setCustomSelected(!!mode);
  };

  useEffect(() => {
    setServerList(data?.usersForUserSelect || []);
  }, [data?.usersForUserSelect]);
  useEffect(() => {
    if (visible && watchReceptionName && !watchReceptionId) {
      setSearchValue(watchReceptionName);
    }
  }, [visible]);

  useEffect(() => {
    if (!watchReceptionName) {
      setSelectedId(null);
      setCustomSelected(false);
      return;
    }

    if (watchReceptionId) {
      setSelectedId(watchReceptionId);
      setCustomSelected(false);
    } else {
      setSelectedId(null);
      setCustomSelected(true);
    }
  }, [watchReceptionName, watchReceptionId, visible]);

  console.log(selectedId, searchValue, '21-12searchValue');
  return (
    <>
      <Form.Item
        label="接待人姓名"
        name="receptionName"
        rules={[
          {
            required: true,
            message: `接待人姓名必填`,
          },
        ]}
        onClick={() => {
          setVisible(true);
        }}
      >
        <Input value={watchReceptionName || ''} readOnly placeholder={'请选择用户'} />
      </Form.Item>
      <Form.Item name="receptionId" hidden />
      <DivStyled>
        <StyledPopup
          title="接待人"
          visible={visible}
          onMaskClick={() => setVisible(false)}
          bodyStyle={{ height: '80vh' }}
          getContainer={null}
          footer={
            <div
              style={{
                margin: '16px',
                display: 'flex',
                justifyContent: 'space-between',
              }}
            >
              <Button
                block
                fill="outline"
                color="primary"
                style={{ width: '100%', marginRight: '12px' }}
                onClick={() => {
                  setSearchValue('');
                  setSelectedId(null);
                  setCustomSelected(false);
                  form.resetFields(['receptionName', 'receptionId']);
                }}
              >
                重置
              </Button>
              <Button
                style={{ width: '100%' }}
                color="primary"
                onClick={() => {
                  const info = userList.find(u => u.id === (selectedId as any));
                  if (customSelected && (!searchValue || searchValue === '')) {
                    // 不进行处理 除非手动重置
                    setVisible(false);
                    return;
                  }
                  if (!customSelected && !info) {
                    form.resetFields(['receptionName', 'receptionId']);
                    setVisible(false);
                    return;
                  }

                  form.setFieldsValue({
                    receptionName: customSelected ? searchValue : info?.name,
                    receptionId: info?.id,
                  });

                  setVisible(false);
                }}
              >
                确定
              </Button>
            </div>
          }
        >
          <Space direction="vertical" style={{ width: '100%' }} wrap>
            <SearchBar
              style={{ width: '100%' }}
              placeholder="请输入姓名查询"
              value={searchValue}
              maxLength={6}
              onChange={handleInputChange}
            />
            <div style={{ paddingTop: 8 }}>
              <Typography.Text type="weak" style={{ fontSize: '12px' }}>
                系统外用户
              </Typography.Text>
              <CheckList
                value={customSelected ? [searchValue] : []}
                onChange={v => {
                  handleSelect(v?.[0], 'extra-system');
                }}
              >
                <CheckList.Item value={searchValue} disabled={!searchValue}>
                  <Typography.Text
                    type={customSelected ? 'primary' : undefined}
                    style={{ fontSize: '16px' }}
                  >
                    {searchValue}
                  </Typography.Text>
                </CheckList.Item>
              </CheckList>
            </div>

            <div style={{ paddingTop: 8 }}>
              <Typography.Text type="weak" style={{ fontSize: '12px' }}>
                系统内用户
              </Typography.Text>
              <div style={{ height: '34vh', overflowY: 'scroll', padding: '0' }}>
                <CheckList
                  value={selectedId ? [selectedId] : []}
                  onChange={val => handleSelect(val?.[0])}
                  style={{ minHeight: '34vh' }}
                >
                  {userList.length === 0 && !loading && <Empty description="暂无用户" />}
                  {userList.map((user: { id: number; name: string }) => (
                    <CheckList.Item key={user.id} value={user.id}>
                      <Typography.Text
                        type={selectedId === user.id ? 'primary' : undefined}
                        style={{ fontSize: '16px' }}
                      >
                        {user.name} {user.id}
                      </Typography.Text>
                    </CheckList.Item>
                  ))}
                </CheckList>
              </div>
            </div>
          </Space>
        </StyledPopup>
      </DivStyled>
    </>
  );
}
