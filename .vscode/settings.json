{"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "typescript.tsserver.experimental.enableProjectDiagnostics": false, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/.hg/store/**": true, "**/.bit/**": true, "**/.yarn/cache/**": true}, "gitlens.remotes": [{"type": "GitLab", "name": "Code - Manyun FET", "domain": "code.dcbase.cn", "protocol": "https"}], "gitlens.autolinks": [{"prefix": "STORY#", "url": "http://chandao.manyun-local.com/zentao/story-view-<num>.html"}, {"prefix": "TASK#", "url": "http://chandao.manyun-local.com/zentao/task-view-<num>.html"}, {"prefix": "ISSUE#", "url": "http://chandao.manyun-local.com/zentao/bug-view-<num>.html"}], "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "files.insertFinalNewline": false}, "[nginx]": {"editor.defaultFormatter": "ahmadalli.vscode-nginx-conf"}, "files.associations": {"*.mdx": "markdown", ".bitmap": "jsonc"}}